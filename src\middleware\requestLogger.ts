import { Request, Response, NextFunction } from 'express';
import logger from '../helpers/logger';

// Enhanced request logging middleware
export const requestLogger = (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();
  const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  // Log incoming request
  const requestLog = {
    id: requestId,
    method: req.method,
    path: req.path,
    originalUrl: req.originalUrl,
    query: req.query,
    params: req.params,
    headers: sanitizeHeaders(req.headers),
    body: sanitizeRequestBody(req.body),
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString()
  };

  logger.info('Incoming Request', requestLog);

  // Override response methods to capture response data
  const originalSend = res.send;
  const originalJson = res.json;
  let responseData: any = null;

  // Capture response data
  res.send = function(data: any) {
    responseData = data;
    return originalSend.call(this, data);
  };

  res.json = function(data: any) {
    responseData = data;
    return originalJson.call(this, data);
  };

  // Log response when finished
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    const responseLog = {
      id: requestId,
      method: req.method,
      path: req.path,
      statusCode: res.statusCode,
      statusMessage: res.statusMessage,
      duration: `${duration}ms`,
      responseSize: responseData ? JSON.stringify(responseData).length : 0,
      responseData: sanitizeResponseData(responseData),
      timestamp: new Date().toISOString()
    };

    // Log based on status code
    if (res.statusCode >= 400) {
      logger.error('Request Completed with Error', responseLog);
    } else {
      logger.info('Request Completed Successfully', responseLog);
    }
  });

  next();
};

// Sanitize sensitive headers
const sanitizeHeaders = (headers: any) => {
  const sensitiveHeaders = ['authorization', 'x-api-key', 'cookie', 'x-auth-token'];
  const sanitized = { ...headers };

  sensitiveHeaders.forEach(header => {
    if (sanitized[header]) {
      sanitized[header] = '[REDACTED]';
    }
  });

  return sanitized;
};

// Sanitize request body (remove passwords, sensitive data)
const sanitizeRequestBody = (body: any): any => {
  if (!body || typeof body !== 'object') return body;

  const sensitiveFields = ['password', 'token', 'secret', 'key', 'apiKey', 'authorization'];
  const sanitized = { ...body };

  sensitiveFields.forEach(field => {
    if (sanitized[field]) {
      sanitized[field] = '[REDACTED]';
    }
  });

  // Limit body size for logging (avoid logging huge payloads)
  const bodyString = JSON.stringify(sanitized);
  if (bodyString.length > 10000) { // 10KB limit
    return {
      ...sanitized,
      _truncated: true,
      _originalSize: bodyString.length,
      _message: 'Body truncated due to size'
    };
  }

  return sanitized;
};

// Sanitize response data (limit size, remove sensitive info)
const sanitizeResponseData = (data: any): any => {
  if (!data) return data;

  try {
    const dataString = JSON.stringify(data);

    // Limit response size for logging
    if (dataString.length > 5000) { // 5KB limit
      return {
        _truncated: true,
        _originalSize: dataString.length,
        _message: 'Response truncated due to size'
      };
    }

    // If it's a string, try to parse and sanitize
    if (typeof data === 'string') {
      try {
        const parsed = JSON.parse(data);
        return sanitizeResponseData(parsed);
      } catch {
        // Not JSON, return as-is if not too long
        return data.length > 100 ? data.substring(0, 100) + '...' : data;
      }
    }

    return data;
  } catch (error) {
    return { _error: 'Failed to sanitize response data', _type: typeof data };
  }
};

// Detailed API logging middleware (for specific routes)
export const detailedApiLogger = (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();

  // Log detailed request information
  logger.info('API Request Details', {
    method: req.method,
    path: req.path,
    query: req.query,
    params: req.params,
    body: sanitizeRequestBody(req.body),
    headers: sanitizeHeaders(req.headers),
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    contentType: req.get('Content-Type'),
    contentLength: req.get('Content-Length'),
    timestamp: new Date().toISOString()
  });

  // Capture response
  const originalJson = res.json;
  res.json = function(data: any) {
    const duration = Date.now() - startTime;

    logger.info('API Response Details', {
      method: req.method,
      path: req.path,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      responseData: sanitizeResponseData(data),
      responseHeaders: res.getHeaders(),
      timestamp: new Date().toISOString()
    });

    return originalJson.call(this, data);
  };

  next();
};

// Performance logging middleware
export const performanceLogger = (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();

  res.on('finish', () => {
    const duration = Date.now() - startTime;
    const logLevel = duration > 5000 ? 'warn' : duration > 1000 ? 'info' : 'debug';

    const perfData = {
      method: req.method,
      path: req.path,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      performance: duration > 5000 ? 'SLOW' : duration > 1000 ? 'MODERATE' : 'FAST',
      timestamp: new Date().toISOString()
    };

    if (logLevel === 'warn') {
      logger.warn('Slow API Performance', perfData);
    } else if (logLevel === 'info') {
      logger.info('API Performance', perfData);
    } else {
      logger.debug('API Performance', perfData);
    }
  });

  next();
};

// Error logging middleware (must be used after other middleware)
export const errorLogger = (error: any, req: Request, res: Response, next: NextFunction) => {
  const errorLog = {
    method: req.method,
    path: req.path,
    query: req.query,
    params: req.params,
    body: sanitizeRequestBody(req.body),
    headers: sanitizeHeaders(req.headers),
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    error: {
      message: error.message,
      stack: error.stack,
      statusCode: error.statusCode || 500,
      isOperational: error.isOperational !== false
    },
    timestamp: new Date().toISOString()
  };

  logger.error('API Error', errorLog);

  next(error);
};

// Request ID middleware (adds unique ID to each request)
export const requestIdMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  (req as any).requestId = requestId;
  res.setHeader('X-Request-ID', requestId);
  next();
};

// Export sanitization functions for use in controllers
export { sanitizeRequestBody, sanitizeResponseData, sanitizeHeaders };
