# 🚚 Batayeq Delivery Microservice

[![Node.js](https://img.shields.io/badge/Node.js-18+-green.svg)](https://nodejs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0+-blue.svg)](https://www.typescriptlang.org/)
[![Express](https://img.shields.io/badge/Express-4.18+-lightgrey.svg)](https://expressjs.com/)
[![MongoDB](https://img.shields.io/badge/MongoDB-7+-green.svg)](https://www.mongodb.com/)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://www.docker.com/)
[![Jest](https://img.shields.io/badge/Jest-Testing-red.svg)](https://jestjs.io/)

A comprehensive, production-ready delivery microservice built with Node.js, Express, TypeScript, and MongoDB. Features Aramex integration, JWT authentication, API key management, and enterprise-grade security.

## ✨ Features

### 🏗️ **Architecture**
- **Layered Architecture**: Clean separation of routes, controllers, services, and models
- **TypeScript**: Full type safety and better developer experience
- **SOLID Principles**: Maintainable and extensible codebase
- **Repository Pattern**: Clean data access layer

### 🔐 **Security & Authentication**
- **JWT Authentication**: Secure token-based authentication
- **API Key Management**: Programmatic access with granular permissions
- **Role-Based Access Control**: Admin, Manager, User, Viewer roles
- **Security Headers**: XSS, CSRF, Content-Type protection
- **Rate Limiting**: Configurable request throttling
- **Input Validation**: Comprehensive data validation and sanitization

### 📦 **Core Functionality**
- **Shipment Creation**: Full shipment lifecycle management
- **Rate Calculation**: Dynamic shipping cost calculation
- **Tracking Integration**: Real-time shipment tracking
- **Aramex Integration**: Official Aramex API integration
- **Webhook Processing**: Automated webhook handling and logging

### 🧪 **Quality Assurance**
- **Unit Tests**: Comprehensive service layer testing
- **Integration Tests**: End-to-end API testing
- **Test Coverage**: Detailed coverage reporting
- **Linting**: ESLint configuration for code quality
- **TypeScript Strict**: Maximum type safety

### 🐳 **DevOps & Deployment**
- **Docker Support**: Containerized deployment
- **Environment Configuration**: Flexible configuration management
- **Health Checks**: Application monitoring endpoints
- **Logging**: Structured logging with Winston
- **Database Migrations**: Automated schema management

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm
- MongoDB (local or cloud)
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone https://gitlab.com/batayeq/batayeq-delivery.git
   cd batayeq-delivery
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration values
   ```

4. **Start MongoDB**
   ```bash
   # Using local MongoDB
   mongod

   # Or using Docker
   docker run -d -p 27017:27017 --name mongodb mongo:7
   ```

5. **Start the server**
   ```bash
   # Development mode (with hot reload)
   npm run dev

   # Production mode
   npm run build
   npm start
   ```

### Environment Configuration

Create a `.env` file with the following variables:

```bash
# Application
APP_PORT=3000
NODE_ENV=development
DOMAIN=localhost

# Database
MONGODB_URL=mongodb://localhost:27017/batayeq_delivery

# JWT
JWT_SECRET=your-super-secret-jwt-key
JWT_REFRESH_SECRET=your-refresh-secret-refresh-key
JWT_ACCESS_EXPIRY=15m
JWT_REFRESH_EXPIRY=7d

# Aramex Integration
ARAMEX_BASE_URL=https://ws.aramex.net/ShippingAPI.V2/
ARAMEX_ADMIN_USER_NAME=your_aramex_username
ARAMEX_ADMIN_PASSWORD=your_aramex_password
ARAMEX_ADMIN_ACCOUNT_NUMBER=your_account_number
ARAMEX_ADMIN_ACCOUNT_PIN=your_account_pin
ARAMEX_ADMIN_ACCOUNT_ENTITY=your_account_entity
ARAMEX_ADMIN_ACCOUNT_COUNTRY_CODE=AE

# Security
CORS_ORIGINS=http://localhost:3000,http://localhost:8080
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

## 📋 API Documentation

### Base URL
```bash
http://localhost:3000
```

### Authentication
The API supports two authentication methods:

#### JWT Bearer Token
```bash
Authorization: Bearer <your-jwt-token>
```

#### API Key
```bash
# Header
X-API-Key: <your-api-key>

# Or Query Parameter
?api_key=<your-api-key>
```

### Endpoints Overview

#### 🔐 Authentication (`/auth/*`)
- `POST /auth/register` - User registration
- `POST /auth/login` - User login
- `POST /auth/refresh` - Refresh access token
- `GET /auth/profile` - Get user profile
- `POST /auth/api-keys` - Create API key
- `GET /auth/api-keys` - List API keys
- `DELETE /auth/api-keys/:id` - Delete API key

#### 📦 Shipping (`/*`)
- `POST /create-shipping` - Create new shipment
- `POST /delivery-rate` - Calculate shipping rates
- `POST /calculate-cost` - Calculate shipping costs
- `GET /tracking/:number` - Track shipment

#### 📋 Pickup (`/*`)
- `POST /create-pickup` - Create pickup request (standard format)
- `POST /create-pickup-simplified` - Create pickup request (simplified format)

#### 🪝 Webhooks (`/webhooks/*`)
- `POST /webhook` - Receive webhooks
- `GET /webhooks/logs` - View webhook logs
- `GET /webhooks/stats` - Webhook statistics

#### 💚 Health (`/*`)
- `GET /ping` - Lightweight health check
- `GET /health` - Comprehensive health check
- `GET /info` - Service information

### Example Requests

#### Create Shipment
```bash
curl -X POST http://localhost:3000/create-shipping \
  -H "Content-Type: application/json" \
  -d '{
    "shipper": {
      "addressLine1": "123 Shipper Street",
      "city": "Dubai",
      "countryCode": "AE",
      "personName": "John Doe",
      "phoneNumber": "+971501234567",
      "emailAddress": "<EMAIL>",
      "companyName": "Shipper Corp"
    },
    "consignee": {
      "addressLine1": "456 Receiver Ave",
      "city": "Abu Dhabi",
      "countryCode": "AE",
      "personName": "Jane Smith",
      "phoneNumber": "+971507654321",
      "emailAddress": "<EMAIL>",
      "companyName": "Receiver Corp"
    },
    "shipmentDetails": {
      "weight": 2.5,
      "dimensions": {"length": 30, "width": 20, "height": 15},
      "numberOfPieces": 1,
      "internalOrderId": "ORD-12345",
      "customerCharges": 150.00
    },
    "references": {
      "reference1": "REF-001"
    }
  }'
```

#### Create Pickup (Simplified Format)
```bash
curl -X POST http://localhost:3000/create-pickup-simplified \
  -H "Content-Type: application/json" \
  -d '{
    "PickupAddress": {
      "Line1": "Test Address",
      "Line2": "Test Address Line 2",
      "City": "Dubai",
      "StateOrProvinceCode": "Dubai",
      "CountryCode": "AE",
      "PostCode": ""
    },
    "PickupContact": {
      "EmailAddress": "<EMAIL>",
      "CellPhone": "97148707700",
      "PersonName": "Test Person Name",
      "CompanyName": "Test Company Name",
      "PhoneNumber1": "97148707700",
      "Department": "Test Department"
    },
    "PickupLocation": "Reception",
    "PickupDate": "2025-09-25T10:00:00.000Z",
    "ReadyTime": "2025-09-25T09:00:00.000Z",
    "LastPickupTime": "2025-09-25T17:00:00.000Z",
    "ClosingTime": "2025-09-25T18:00:00.000Z",
    "PickupItems": [{
      "ProductGroup": "DOM",
      "ProductType": "ONP",
      "NumberOfShipments": 1,
      "PackageType": "Box",
      "Payment": "P",
      "ShipmentWeight": {
        "Unit": "KG",
        "Value": 0.5
      },
      "NumberOfPieces": 1,
      "ShipmentDimensions": {
        "Length": 10,
        "Width": 10,
        "Height": 10,
        "Unit": "CM"
      },
      "Comments": "Test pickup item"
    }],
    "Status": "Ready",
    "Comments": "Test pickup request",
    "Vehicle": "Bike",
    "References": {
      "Reference1": "001",
      "Reference2": "Test Reference"
    }
  }'
```

#### Calculate Rate
```bash
curl -X POST http://localhost:3000/delivery-rate \
  -H "Content-Type: application/json" \
  -d '{
    "OriginAddress": {
      "Line1": "Dubai Mall",
      "City": "Dubai",
      "CountryCode": "AE"
    },
    "DestinationAddress": {
      "Line1": "Yas Mall",
      "City": "Abu Dhabi",
      "CountryCode": "AE"
    },
    "ShipmentDetails": {
      "ActualWeight": 2.5,
      "ChargeableWeight": 3.0
    },
    "PreferredCurrencyCode": "AED"
  }'
```

## 🧪 Testing

### Run Tests
```bash
# Run all tests
npm test

# Run with coverage
npm run test:coverage

# Run in watch mode
npm run test:watch

# Run CI tests
npm run test:ci
```

### Test Structure
```bash
tests/
├── setup.ts              # Global test configuration
├── unit/
│   └── services/
│       ├── authService.test.ts
│       └── webhookService.test.ts
└── integration/
    └── auth.test.ts      # API integration tests
```

### Postman Collection
Import `Batayeq_Delivery_API.postman_collection.json` for comprehensive API testing.

## 🐳 Docker Deployment

### Using Docker Compose
```bash
# Build and run with MongoDB
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Manual Docker Build
```bash
# Build image
docker build -t batayeq-delivery .

# Run container
docker run -d \
  --name batayeq-delivery \
  -p 3000:3000 \
  -e MONGODB_URL=mongodb://host.docker.internal:27017/batayeq_delivery \
  batayeq-delivery
```

## 📊 Monitoring & Health Checks

### Health Endpoints
- `GET /ping` - Basic connectivity check
- `GET /health` - Comprehensive health check
- `GET /info` - Service metadata

### Logging
- Structured logging with Winston
- Configurable log levels
- File and console output
- Request/response logging

## 🔧 Development

### Available Scripts
```bash
npm run build      # Build TypeScript to JavaScript
npm run dev        # Start development server with hot reload
npm run start      # Start production server
npm run test       # Run tests
npm run lint       # Run ESLint
npm run validate-setup  # Validate environment setup
```

### Project Structure
```bash
src/
├── config/           # Configuration files
├── controllers/      # HTTP request handlers
├── helpers/          # Utility functions
├── middleware/       # Express middleware
├── models/           # MongoDB schemas
├── routes/           # Route definitions
├── services/         # Business logic layer
└── server.ts         # Application entry point

tests/                # Test files
├── setup.ts         # Test configuration
├── unit/            # Unit tests
└── integration/     # Integration tests
```

### Code Quality
- **TypeScript**: Strict mode enabled
- **ESLint**: Configured for code quality
- **Prettier**: Code formatting
- **Husky**: Pre-commit hooks (optional)

## 🚀 Production Deployment

### Environment Variables
Ensure all production environment variables are set:

```bash
NODE_ENV=production
JWT_SECRET=<strong-production-secret>
ARAMEX_ADMIN_USER_NAME=<real-credentials>
# ... all other required variables
