import { Response } from 'express';

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  timestamp: string;
}

export interface ErrorResponse {
  success: false;
  message: string;
  error: string;
  timestamp: string;
  statusCode: number;
}

export class ApiError extends Error {
  public statusCode: number;
  public isOperational: boolean;

  constructor(message: string, statusCode: number = 500, isOperational: boolean = true) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;

    Error.captureStackTrace(this, this.constructor);
  }
}

export const sendSuccess = <T>(
  res: Response,
  message: string,
  data?: T,
  statusCode: number = 200
): void => {
  const response: ApiResponse<T> = {
    success: true,
    message,
    ...(data !== undefined && { data }),
    timestamp: new Date().toISOString(),
  };

  res.status(statusCode).json(response);
};

export const sendError = (
  res: Response,
  error: ApiError | Error | string,
  statusCode: number = 500
): void => {
  let message = 'Internal server error';
  let errorDetails = 'An unexpected error occurred';

  if (error instanceof ApiError) {
    message = error.message;
    errorDetails = error.message;
    statusCode = error.statusCode;
  } else if (error instanceof Error) {
    message = 'Internal server error';
    errorDetails = error.message;
  } else if (typeof error === 'string') {
    message = error;
    errorDetails = error;
  }

  const response: ErrorResponse = {
    success: false,
    message,
    error: errorDetails,
    timestamp: new Date().toISOString(),
    statusCode,
  };

  // Log error for debugging (only in development)
  if (process.env.NODE_ENV === 'development') {
    console.error('API Error:', {
      message,
      error: errorDetails,
      statusCode,
      stack: error instanceof Error ? error.stack : undefined,
    });
  }

  res.status(statusCode).json(response);
};

export const asyncHandler = (fn: Function) => {
  return (req: any, res: any, next: any) => {
    Promise.resolve(fn(req, res, next)).catch((error) => {
      sendError(res, error);
    });
  };
};
