import * as bodyParser from "body-parser";
import config from "./config/appConfig";
import dbConnection from "./db/connection";
import App from "./helpers/appServer";
import logger from "./helpers/logger";
import AramexWebhookRoute from "./routes/aromax-webhook";
import DefaultRoute from "./routes/defaultRoute";
import DeliveryRateRoute from "./routes/rate-calculate";
import PickupRoute from "./routes/pickupRoute";
import ShippingRoute from "./routes/shippingRoute";
import { securityHeaders, rateLimit, sanitizeInput } from "./middleware/security";
import { requestLogger, errorLogger, performanceLogger } from "./middleware/requestLogger";
import cors = require("cors");

const bootServer = async () => {
  try {
    await dbConnection();
    logger.info("Database connection established");

    const appInstance = new App({
      port: config.app.port,
      defaults: [cors()],
      middleWares: [
        requestLogger,
        performanceLogger,
        securityHeaders,
        rateLimit,
        sanitizeInput,
        bodyParser.json({ limit: '10mb' }),
        bodyParser.urlencoded({ extended: true, limit: '10mb' })
      ],
      routes: [
        new AramexWebhookRoute(),
        new ShippingRoute(),
        new PickupRoute(),
        new DeliveryRateRoute(),
        new DefaultRoute(),
      ],
    });

    // Add error logging middleware (must be last)
    (appInstance.app as any).use(errorLogger);

    appInstance.listen();
    logger.info(`Server started successfully on port ${config.app.port}`);
  } catch (error) {
    logger.error("Failed to start server", error);
    process.exit(1);
  }
};

bootServer();
