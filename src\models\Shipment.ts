import mongoose, { Schema, Document } from 'mongoose';

export interface IShipmentAddress {
  line1: string;
  line2?: string;
  city: string;
  stateOrProvinceCode?: string;
  postCode: string;
  countryCode: string;
}

export interface IShipmentContact {
  personName: string;
  companyName: string;
  phoneNumber: string;
  cellPhone?: string;
  emailAddress: string;
}

export interface IShipmentParty {
  reference1?: string;
  reference2?: string;
  accountNumber: string;
  address: IShipmentAddress;
  contact: IShipmentContact;
}

export interface IShipmentDetails {
  weight: number;
  dimensions: {
    length: number;
    width: number;
    height: number;
  };
  numberOfPieces: number;
  internalOrderId?: string;
  customerCharges?: number;
  descriptionOfGoods?: string;
  goodsOriginCountry?: string;
}

export interface ITrackingEvent {
  date: Date;
  status: string;
  location: string;
  description?: string;
  carrierSpecificData?: any;
}

export type ShipmentStatus =
  | 'draft'
  | 'pending'
  | 'confirmed'
  | 'in_transit'
  | 'delivered'
  | 'cancelled'
  | 'failed';

export interface IShipment extends Document {
  // Shipment identifiers
  reference1?: string;
  reference2?: string;
  reference3?: string;
  trackingNumber?: string;
  aramexShipmentId?: string;

  // Parties
  shipper: IShipmentParty;
  consignee: IShipmentParty;
  thirdParty?: IShipmentParty;

  // Shipment details
  details: IShipmentDetails;

  // Status and tracking
  status: ShipmentStatus;
  statusHistory: {
    status: ShipmentStatus;
    timestamp: Date;
    description?: string;
  }[];
  trackingEvents: ITrackingEvent[];

  // Aramex integration
  aramexPayload?: any;
  aramexResponse?: any;
  lastSyncWithAramex?: Date;

  // Business metadata
  createdBy?: string;
  customerId?: string;
  cost?: number;
  currency?: string;

  // Timestamps
  shippingDate?: Date;
  deliveryDate?: Date;
  createdAt: Date;
  updatedAt: Date;
}

const ShipmentAddressSchema = new Schema<IShipmentAddress>({
  line1: { type: String, required: true, trim: true },
  line2: { type: String, trim: true },
  city: { type: String, required: true, trim: true },
  stateOrProvinceCode: { type: String, trim: true },
  postCode: { type: String, required: true, trim: true },
  countryCode: { type: String, required: true, trim: true, uppercase: true }
}, { _id: false });

const ShipmentContactSchema = new Schema<IShipmentContact>({
  personName: { type: String, required: true, trim: true },
  companyName: { type: String, required: true, trim: true },
  phoneNumber: { type: String, required: true, trim: true },
  cellPhone: { type: String, trim: true },
  emailAddress: { type: String, required: true, trim: true, lowercase: true }
}, { _id: false });

const ShipmentPartySchema = new Schema<IShipmentParty>({
  reference1: { type: String, trim: true },
  reference2: { type: String, trim: true },
  accountNumber: { type: String, required: true, trim: true },
  address: { type: ShipmentAddressSchema, required: true },
  contact: { type: ShipmentContactSchema, required: true }
}, { _id: false });

const ShipmentDetailsSchema = new Schema<IShipmentDetails>({
  weight: { type: Number, required: true, min: 0.1 },
  dimensions: {
    length: { type: Number, required: true, min: 1 },
    width: { type: Number, required: true, min: 1 },
    height: { type: Number, required: true, min: 1 }
  },
  numberOfPieces: { type: Number, required: true, min: 1 },
  internalOrderId: { type: String, trim: true },
  customerCharges: { type: Number, min: 0 },
  descriptionOfGoods: { type: String, trim: true, default: 'Items' },
  goodsOriginCountry: { type: String, trim: true, uppercase: true, default: 'AE' }
}, { _id: false });

const TrackingEventSchema = new Schema<ITrackingEvent>({
  date: { type: Date, required: true },
  status: { type: String, required: true, trim: true },
  location: { type: String, required: true, trim: true },
  description: { type: String, trim: true },
  carrierSpecificData: { type: Schema.Types.Mixed }
}, { _id: false });

const ShipmentSchema = new Schema<IShipment>({
  // Identifiers
  reference1: { type: String, trim: true },
  reference2: { type: String, trim: true },
  reference3: { type: String, trim: true },
  trackingNumber: { type: String, trim: true },
  aramexShipmentId: { type: String, trim: true },

  // Parties
  shipper: { type: ShipmentPartySchema, required: true },
  consignee: { type: ShipmentPartySchema, required: true },
  thirdParty: { type: ShipmentPartySchema },

  // Shipment details
  details: { type: ShipmentDetailsSchema, required: true },

  // Status and tracking
  status: {
    type: String,
    enum: ['draft', 'pending', 'confirmed', 'in_transit', 'delivered', 'cancelled', 'failed'],
    default: 'draft',
    required: true
  },
  statusHistory: [{
    status: {
      type: String,
      enum: ['draft', 'pending', 'confirmed', 'in_transit', 'delivered', 'cancelled', 'failed'],
      required: true
    },
    timestamp: { type: Date, default: Date.now, required: true },
    description: { type: String, trim: true }
  }],
  trackingEvents: [TrackingEventSchema],

  // Aramex integration
  aramexPayload: { type: Schema.Types.Mixed },
  aramexResponse: { type: Schema.Types.Mixed },
  lastSyncWithAramex: { type: Date },

  // Business metadata
  createdBy: { type: String, trim: true },
  customerId: { type: String, trim: true },
  cost: { type: Number, min: 0 },
  currency: { type: String, trim: true, uppercase: true, default: 'AED' },

  // Dates
  shippingDate: { type: Date },
  deliveryDate: { type: Date }
}, {
  timestamps: true,
  collection: 'shipments'
});

// Indexes for performance
ShipmentSchema.index({ trackingNumber: 1 }, { unique: true, sparse: true });
ShipmentSchema.index({ aramexShipmentId: 1 }, { sparse: true });
ShipmentSchema.index({ status: 1, createdAt: -1 });
ShipmentSchema.index({ 'shipper.contact.emailAddress': 1 });
ShipmentSchema.index({ 'consignee.contact.emailAddress': 1 });
ShipmentSchema.index({ createdBy: 1, createdAt: -1 });
ShipmentSchema.index({ customerId: 1, createdAt: -1 });
ShipmentSchema.index({ lastSyncWithAramex: -1 });

// Virtual for chargeable weight calculation
ShipmentSchema.virtual('chargeableWeight').get(function() {
  const { weight, dimensions } = this.details;
  const volumetricWeight = (dimensions.length * dimensions.width * dimensions.height) / 5000;
  return Math.max(weight, volumetricWeight);
});

// Instance method to update status
ShipmentSchema.methods.updateStatus = function(newStatus: ShipmentStatus, description?: string) {
  this.status = newStatus;
  this.statusHistory.push({
    status: newStatus,
    timestamp: new Date(),
    description
  });
  return this.save();
};

// Static method to find by tracking number
ShipmentSchema.statics.findByTrackingNumber = function(trackingNumber: string) {
  return this.findOne({ trackingNumber });
};

export const Shipment = mongoose.model<IShipment>('Shipment', ShipmentSchema);
