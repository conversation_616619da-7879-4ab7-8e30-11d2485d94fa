import * as mongoose from 'mongoose';
import config from '../config/appConfig';

let isConnected: boolean = false; // Track the connection status

export default async () => {
  if (isConnected) {
    console.log('Already connected to the database');
    return;
  }

  const dbUrl: string = config.dataBase.url;
  const options: mongoose.ConnectOptions = {};

  try {
    await mongoose.connect(dbUrl, options);
    isConnected = true; // Set the connection status to true
    console.log(`Successfully connected to MongoDB`);
  } catch (error) {
    console.log("Error connecting to database: ", error);
    process.exit(1);
  }

  mongoose.connection.on("disconnected", () => {
    isConnected = false; // Reset the connection status
    console.log('MongoDB disconnected');
  });

  process.on('SIGINT', async () => {
    await mongoose.connection.close();
    console.log('Mongoose disconnected through app termination');
    process.exit(0);
  });
};