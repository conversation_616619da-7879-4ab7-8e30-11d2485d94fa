import { describe, it, expect, beforeEach } from '@jest/globals';
import { PickupService, ISimplifiedPickupRequest } from '../src/services/shippingService';

describe('Simplified Pickup Service', () => {
  let pickupService: PickupService;

  beforeEach(() => {
    pickupService = PickupService.getInstance();
  });

  describe('validateSimplifiedPickupData', () => {
    it('should validate a valid simplified pickup request', () => {
      const validRequest: ISimplifiedPickupRequest = {
        PickupAddress: {
          Line1: 'Test Address',
          Line2: 'Test Address Line 2',
          City: 'Dubai',
          StateOrProvinceCode: 'Dubai',
          CountryCode: 'AE',
          PostCode: ''
        },
        PickupContact: {
          EmailAddress: '<EMAIL>',
          CellPhone: '97148707700',
          PersonName: 'Test Person',
          CompanyName: 'Test Company',
          PhoneNumber1: '97148707700',
          Department: 'Test Department'
        },
        PickupLocation: 'Reception',
        PickupDate: '2025-09-25T10:00:00.000Z',
        ReadyTime: '2025-09-25T09:00:00.000Z',
        LastPickupTime: '2025-09-25T17:00:00.000Z',
        ClosingTime: '2025-09-25T18:00:00.000Z',
        PickupItems: [{
          ProductGroup: 'DOM',
          ProductType: 'ONP',
          NumberOfShipments: 1,
          PackageType: 'Box',
          Payment: 'P',
          ShipmentWeight: {
            Unit: 'KG',
            Value: 0.5
          },
          NumberOfPieces: 1,
          ShipmentDimensions: {
            Length: 10,
            Width: 10,
            Height: 10,
            Unit: 'CM'
          },
          Comments: 'Test item'
        }],
        Status: 'Ready'
      };

      const result = pickupService.validateSimplifiedPickupData(validRequest);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should fail validation for missing required fields', () => {
      const invalidRequest: Partial<ISimplifiedPickupRequest> = {
        PickupAddress: {
          Line1: 'Test Address',
          City: 'Dubai',
          CountryCode: 'AE'
        }
        // Missing PickupContact, PickupLocation, dates, and PickupItems
      };

      const result = pickupService.validateSimplifiedPickupData(invalidRequest as ISimplifiedPickupRequest);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors).toContain('Pickup contact email address is required');
      expect(result.errors).toContain('Pickup contact cell phone is required');
      expect(result.errors).toContain('Pickup location is required');
      expect(result.errors).toContain('At least one pickup item is required');
    });

    it('should fail validation for invalid pickup items', () => {
      const invalidRequest: ISimplifiedPickupRequest = {
        PickupAddress: {
          Line1: 'Test Address',
          City: 'Dubai',
          CountryCode: 'AE'
        },
        PickupContact: {
          EmailAddress: '<EMAIL>',
          CellPhone: '123456789'
        },
        PickupLocation: 'Reception',
        PickupDate: '2025-09-25T10:00:00.000Z',
        ReadyTime: '2025-09-25T09:00:00.000Z',
        LastPickupTime: '2025-09-25T17:00:00.000Z',
        ClosingTime: '2025-09-25T18:00:00.000Z',
        PickupItems: [{
          ProductGroup: '', // Invalid - empty
          ProductType: 'ONP',
          NumberOfShipments: 0, // Invalid - zero
          PackageType: 'Box',
          Payment: 'P',
          ShipmentWeight: {
            Unit: 'KG',
            Value: -1 // Invalid - negative
          },
          NumberOfPieces: 0, // Invalid - zero
          ShipmentDimensions: {
            Length: 10,
            Width: 10,
            Height: 10,
            Unit: 'CM'
          }
        }]
      };

      const result = pickupService.validateSimplifiedPickupData(invalidRequest);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Pickup item 1: Product group is required');
      expect(result.errors).toContain('Pickup item 1: Valid number of shipments is required');
      expect(result.errors).toContain('Pickup item 1: Valid shipment weight is required');
      expect(result.errors).toContain('Pickup item 1: Valid number of pieces is required');
    });
  });

  describe('transformation methods', () => {
    it('should transform simplified request to standard format', () => {
      const simplifiedRequest: ISimplifiedPickupRequest = {
        PickupAddress: {
          Line1: 'Test Address',
          Line2: 'Test Address Line 2',
          City: 'Dubai',
          StateOrProvinceCode: 'Dubai',
          CountryCode: 'AE',
          PostCode: '12345'
        },
        PickupContact: {
          EmailAddress: '<EMAIL>',
          CellPhone: '97148707700',
          PersonName: 'Test Person',
          CompanyName: 'Test Company',
          PhoneNumber1: '97148707700',
          Department: 'Test Department'
        },
        PickupLocation: 'Reception',
        PickupDate: '2025-09-25T10:00:00.000Z',
        ReadyTime: '2025-09-25T09:00:00.000Z',
        LastPickupTime: '2025-09-25T17:00:00.000Z',
        ClosingTime: '2025-09-25T18:00:00.000Z',
        PickupItems: [{
          ProductGroup: 'DOM',
          ProductType: 'ONP',
          NumberOfShipments: 1,
          PackageType: 'Box',
          Payment: 'P',
          ShipmentWeight: {
            Unit: 'KG',
            Value: 0.5
          },
          NumberOfPieces: 1,
          ShipmentDimensions: {
            Length: 10,
            Width: 10,
            Height: 10,
            Unit: 'CM'
          },
          Comments: 'Test item'
        }],
        Comments: 'Test pickup',
        Vehicle: 'Bike',
        References: {
          Reference1: '001',
          Reference2: 'Test Ref'
        }
      };

      // Access the private method through type assertion for testing
      const transformedRequest = (pickupService as any).transformSimplifiedToStandard(simplifiedRequest);

      expect(transformedRequest.pickupAddress.addressLine1).toBe('Test Address');
      expect(transformedRequest.pickupAddress.city).toBe('Dubai');
      expect(transformedRequest.pickupContact.emailAddress).toBe('<EMAIL>');
      expect(transformedRequest.pickupContact.personName).toBe('Test Person');
      expect(transformedRequest.pickupLocation).toBe('Reception');
      expect(transformedRequest.pickupItems).toHaveLength(1);
      expect(transformedRequest.pickupItems[0].productGroup).toBe('DOM');
      expect(transformedRequest.references?.reference1).toBe('001');
    });
  });
});
