import mongoose, { Schema, Document } from 'mongoose';
import * as crypto from 'crypto';

export interface IApiKey extends Document {
  name: string;
  key: string;
  hashedKey: string;
  description?: string;
  createdBy: string;
  permissions: string[];
  isActive: boolean;
  expiresAt?: Date;
  lastUsed?: Date;
  usageCount: number;
  rateLimit?: {
    requests: number;
    windowMs: number;
  };
  allowedIPs?: string[];
  createdAt: Date;
  updatedAt: Date;

  // Instance methods
  verifyKey(candidateKey: string): boolean;
  isExpired(): boolean;
  canAccessIP(ip: string): boolean;
  incrementUsage(): Promise<IApiKey>;
}

const ApiKeySchema = new Schema<IApiKey>({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  key: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  hashedKey: {
    type: String,
    required: true
  },
  description: {
    type: String,
    trim: true,
    maxlength: 500
  },
  createdBy: {
    type: String,
    required: true,
    trim: true
  },
  permissions: [{
    type: String,
    enum: [
      'shipments:create',
      'shipments:read',
      'shipments:update',
      'shipments:delete',
      'rates:calculate',
      'tracking:read',
      'webhooks:manage',
      'admin:*'
    ],
    required: true
  }],
  isActive: {
    type: Boolean,
    default: true,
    index: true
  },
  expiresAt: {
    type: Date,
    index: true
  },
  lastUsed: {
    type: Date
  },
  usageCount: {
    type: Number,
    default: 0
  },
  rateLimit: {
    requests: { type: Number, min: 1, default: 100 },
    windowMs: { type: Number, min: 1000, default: 900000 } // 15 minutes
  },
  allowedIPs: [{
    type: String,
    trim: true,
    validate: {
      validator: function(ip: string) {
        // Basic IP validation regex
        const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        return ipRegex.test(ip) || ip === '*';
      },
      message: 'Invalid IP address format'
    }
  }]
}, {
  timestamps: true,
  collection: 'api_keys'
});

// Indexes
ApiKeySchema.index({ key: 1 }, { unique: true });
ApiKeySchema.index({ hashedKey: 1 });
ApiKeySchema.index({ createdBy: 1 });
ApiKeySchema.index({ permissions: 1 });
ApiKeySchema.index({ expiresAt: 1 });
ApiKeySchema.index({ lastUsed: -1 });

// Pre-save middleware to hash the API key
ApiKeySchema.pre('save', function(next) {
  if (!this.isModified('key')) return next();

  try {
    // Generate hash of the API key for storage
    this.hashedKey = crypto.createHash('sha256').update(this.key).digest('hex');
    // For security, don't store the plain key after hashing
    this.key = '';
    next();
  } catch (error: any) {
    next(error);
  }
});

// Instance method to verify API key
ApiKeySchema.methods.verifyKey = function(candidateKey: string): boolean {
  if (!candidateKey || !this.hashedKey) return false;

  const candidateHash = crypto.createHash('sha256').update(candidateKey).digest('hex');
  return candidateHash === this.hashedKey;
};

// Instance method to check if key is expired
ApiKeySchema.methods.isExpired = function(): boolean {
  return this.expiresAt ? new Date() > this.expiresAt : false;
};

// Instance method to check if IP is allowed
ApiKeySchema.methods.canAccessIP = function(ip: string): boolean {
  if (!this.allowedIPs || this.allowedIPs.length === 0) return true;
  return this.allowedIPs.includes('*') || this.allowedIPs.includes(ip);
};

// Instance method to increment usage count
ApiKeySchema.methods.incrementUsage = function(): Promise<IApiKey> {
  this.usageCount += 1;
  this.lastUsed = new Date();
  return this.save();
};

// Static method to find API key by hashed key
ApiKeySchema.statics.findByHashedKey = function(hashedKey: string) {
  return this.findOne({ hashedKey, isActive: true });
};

// Static method to generate a new API key
ApiKeySchema.statics.generateKey = function(): string {
  const crypto = require('crypto');
  return crypto.randomBytes(32).toString('hex');
};

// Transform output to exclude sensitive fields
ApiKeySchema.methods.toJSON = function() {
  const apiKeyObject = this.toObject();
  delete apiKeyObject.hashedKey;
  delete apiKeyObject.key; // Plain key is not stored anyway
  return apiKeyObject;
};

// Define the static methods interface
interface IApiKeyModel extends mongoose.Model<IApiKey> {
  findByHashedKey(hashedKey: string): Promise<IApiKey | null>;
  generateKey(): string;
}

export const ApiKey = mongoose.model<IApiKey, IApiKeyModel>('ApiKey', ApiKeySchema);
