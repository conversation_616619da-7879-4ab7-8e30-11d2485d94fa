import { Request, Response } from 'express';
import { sendSuccess, sendError, asyncHandler } from '../helpers/apiResponse';
import { validateObject } from '../helpers/validation';
import { webhookService, IWebhookPayload } from '../services/webhookService';
import logger from '../helpers/logger';

export class WebhookController {
  /**
   * Handle incoming webhook
   */
  public handleWebhook = asyncHandler(async (req: Request, res: Response) => {
    logger.info('Webhook request received', {
      method: req.method,
      url: req.url,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      contentType: req.get('Content-Type')
    });

    // Validate request body
    validateObject(req.body, 'webhook payload');

    const payload: IWebhookPayload = req.body;

    // Process webhook using service
    const result = await webhookService.processWebhook(payload);

    if (result.success) {
      logger.info('Webhook processed successfully', {
        payloadSize: JSON.stringify(payload).length,
        loggedToFile: result.loggedToFile,
        loggedToDatabase: result.loggedToDatabase
      });

      sendSuccess(res, result.message, {
        processed: true,
        loggedToFile: result.loggedToFile,
        loggedToDatabase: result.loggedToDatabase
      });
    } else {
      logger.error('Webhook processing failed', { error: result.message });
      sendError(res, result.message, 500);
    }
  });

  /**
   * Get webhook logs with pagination
   */
  public getWebhookLogs = asyncHandler(async (req: Request, res: Response) => {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const startDate = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
    const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined;

    logger.info('Get webhook logs request', {
      method: req.method,
      url: req.url,
      page,
      limit,
      startDate: startDate?.toISOString(),
      endDate: endDate?.toISOString(),
      ip: req.ip
    });

    const result = await webhookService.getWebhookLogs(page, limit, startDate, endDate);

    sendSuccess(res, 'Webhook logs retrieved successfully', result);
  });

  /**
   * Get webhook statistics
   */
  public getWebhookStats = asyncHandler(async (req: Request, res: Response) => {
    const startDate = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
    const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined;

    logger.info('Get webhook stats request', {
      method: req.method,
      url: req.url,
      startDate: startDate?.toISOString(),
      endDate: endDate?.toISOString(),
      ip: req.ip
    });

    const stats = await webhookService.getWebhookStats(startDate, endDate);

    sendSuccess(res, 'Webhook statistics retrieved successfully', stats);
  });

  /**
   * Test webhook endpoint
   */
  public testWebhook = asyncHandler(async (req: Request, res: Response) => {
    logger.info('Test webhook request received', {
      method: req.method,
      url: req.url,
      ip: req.ip
    });

    const testPayload: IWebhookPayload = {
      test: true,
      timestamp: new Date().toISOString(),
      message: 'This is a test webhook',
      data: {
        orderId: 'TEST-123',
        status: 'test'
      }
    };

    const result = await webhookService.processWebhook(testPayload);

    if (result.success) {
      sendSuccess(res, 'Test webhook processed successfully', {
        testPayload,
        processed: true,
        loggedToFile: result.loggedToFile,
        loggedToDatabase: result.loggedToDatabase
      });
    } else {
      sendError(res, 'Test webhook processing failed', 500);
    }
  });
}

// Export singleton instance
export const webhookController = new WebhookController();
