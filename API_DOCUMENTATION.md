# 🚀 Batayeq Delivery Microservice - API Documentation

## 📋 **Overview**

This document provides comprehensive API documentation for the Batayeq Delivery Microservice, a production-ready shipping and delivery management system built with Node.js, Express, and TypeScript.

## 🔐 **Authentication**

### **Authentication Methods**
The API supports multiple authentication methods:

1. **JWT Bearer Tokens** - For user authentication
2. **API Keys** - For programmatic access
3. **Combined Authentication** - Support both methods

### **Headers**
```http
# JWT Authentication
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# API Key Authentication
X-API-Key: your-api-key-here

# Or via query parameter
GET /endpoint?api_key=your-api-key-here
```

---

## 📚 **API Reference**

### **1. Authentication Endpoints**

#### **POST** `/auth/login`
User authentication with username/email and password.

**Request Body:**
```json
{
  "identifier": "username_or_email",
  "password": "user_password"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": "user_id",
      "username": "username",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "role": "user"
    },
    "tokens": {
      "accessToken": "jwt_access_token",
      "refreshToken": "jwt_refresh_token",
      "expiresIn": 900
    }
  }
}
```

#### **POST** `/auth/register`
Create a new user account.

**Request Body:**
```json
{
  "username": "unique_username",
  "email": "<EMAIL>",
  "password": "secure_password",
  "firstName": "John",
  "lastName": "Doe",
  "role": "user"
}
```

#### **POST** `/auth/refresh`
Refresh access token using refresh token.

**Request Body:**
```json
{
  "refreshToken": "jwt_refresh_token"
}
```

#### **GET** `/auth/profile`
Get current authenticated user profile.

**Headers:** `Authorization: Bearer <token>`

#### **POST** `/auth/api-keys`
Create a new API key for programmatic access.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "name": "My Application",
  "description": "API key for mobile app",
  "permissions": ["shipments:create", "shipments:read"],
  "expiresAt": "2024-12-31T23:59:59.000Z",
  "allowedIPs": ["***********/24"]
}
```

---

### **2. Shipping Endpoints**

#### **POST** `/create-shipping`
Create a new shipment with Aramex integration.

**Headers:** `Authorization: Bearer <token>` or `X-API-Key: <key>`

**Request Body:**
```json
{
  "shipper": {
    "addressLine1": "123 Shipper Street",
    "addressLine2": "Building A, Floor 5",
    "city": "Dubai",
    "stateOrProvinceCode": "DU",
    "postCode": "12345",
    "countryCode": "AE",
    "personName": "John Doe",
    "phoneNumber": "+971501234567",
    "cellPhone": "+971501234567",
    "emailAddress": "<EMAIL>",
    "companyName": "Shipper Corporation"
  },
  "consignee": {
    "addressLine1": "456 Receiver Avenue",
    "addressLine2": "Suite 100",
    "city": "Abu Dhabi",
    "stateOrProvinceCode": "AD",
    "postCode": "67890",
    "countryCode": "AE",
    "personName": "Jane Smith",
    "phoneNumber": "+971507654321",
    "cellPhone": "+971507654321",
    "emailAddress": "<EMAIL>",
    "companyName": "Receiver Corporation"
  },
  "shipmentDetails": {
    "weight": 2.5,
    "dimensions": {
      "length": 30,
      "width": 20,
      "height": 15
    },
    "numberOfPieces": 1,
    "internalOrderId": "ORD-12345",
    "customerCharges": 150.00
  },
  "references": {
    "reference1": "REF-001",
    "reference2": "Customer Order #123",
    "reference3": "Priority Shipment"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Shipment created successfully",
  "data": {
    "shipment": { /* Aramex shipment response */ },
    "trackingNumber": "1234567890",
    "reference": "REF-001"
  }
}
```

#### **POST** `/delivery-rate`
Calculate shipping rate between two locations.

**Headers:** `Authorization: Bearer <token>` or `X-API-Key: <key>`

**Request Body:**
```json
{
  "OriginAddress": {
    "Line1": "123 Origin Street",
    "City": "Dubai",
    "CountryCode": "AE"
  },
  "DestinationAddress": {
    "Line1": "456 Destination Avenue",
    "City": "Abu Dhabi",
    "CountryCode": "AE"
  },
  "ShipmentDetails": {
    "ActualWeight": 2.5,
    "ChargeableWeight": 3.0
  },
  "PreferredCurrencyCode": "AED"
}
```

#### **POST** `/calculate-cost`
Calculate estimated shipping cost.

**Headers:** `Authorization: Bearer <token>` or `X-API-Key: <key>`

**Request Body:** (Same as create-shipping)

#### **GET** `/tracking/{trackingNumber}`
Get shipment tracking information.

**Headers:** `Authorization: Bearer <token>` or `X-API-Key: <key>`

**Response:**
```json
{
  "success": true,
  "message": "Tracking information retrieved",
  "data": {
    "trackingNumber": "1234567890",
    "status": "In Transit",
    "location": "Dubai, UAE",
    "estimatedDelivery": "2024-01-15T10:00:00.000Z",
    "events": [
      {
        "date": "2024-01-10T08:00:00.000Z",
        "status": "Picked up",
        "location": "Dubai, UAE",
        "description": "Package collected from shipper"
      }
    ]
  }
}
```

---

### **3. Webhook Endpoints**

#### **POST** `/webhook`
Receive and process webhooks from external services.

**Headers:** Optional authentication

**Request Body:** Any JSON payload

**Response:**
```json
{
  "success": true,
  "message": "Webhook received and logged successfully",
  "data": {
    "processed": true,
    "loggedToFile": true,
    "loggedToDatabase": true
  }
}
```

#### **GET** `/webhooks/logs`
Retrieve webhook processing logs.

**Headers:** `Authorization: Bearer <token>` or `X-API-Key: <key>`

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 10)
- `startDate` (string): ISO date string
- `endDate` (string): ISO date string

#### **GET** `/webhooks/stats`
Get webhook processing statistics.

**Headers:** `Authorization: Bearer <token>` or `X-API-Key: <key>`

**Response:**
```json
{
  "success": true,
  "message": "Webhook statistics retrieved successfully",
  "data": {
    "totalWebhooks": 1250,
    "successfulLogs": 1245,
    "failedLogs": 5,
    "averagePayloadSize": 2048
  }
}
```

---

### **4. Health & Monitoring Endpoints**

#### **GET** `/ping`
Lightweight health check.

**Response:**
```json
{
  "success": true,
  "message": "pong",
  "data": {
    "timestamp": "2024-01-10T12:00:00.000Z"
  }
}
```

#### **GET** `/health`
Comprehensive health check.

**Response:**
```json
{
  "success": true,
  "message": "Service is healthy",
  "data": {
    "status": "healthy",
    "timestamp": "2024-01-10T12:00:00.000Z",
    "version": "1.0.0",
    "environment": "production",
    "uptime": 86400,
    "memory": {
      "rss": 104857600,
      "heapTotal": 67108864,
      "heapUsed": 45000000,
      "external": 2000000
    },
    "services": {
      "database": "connected",
      "aramex": "configured"
    }
  }
}
```

#### **GET** `/info`
Service information and available endpoints.

---

## 📊 **Response Formats**

### **Success Response**
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": { /* Response data */ },
  "timestamp": "2024-01-10T12:00:00.000Z"
}
```

### **Error Response**
```json
{
  "success": false,
  "message": "Error message",
  "error": "Detailed error description",
  "timestamp": "2024-01-10T12:00:00.000Z"
}
```

### **Validation Error Response**
```json
{
  "success": false,
  "message": "Validation failed",
  "error": "Request validation error",
  "details": [
    "field_name is required",
    "email must be a valid email address"
  ],
  "timestamp": "2024-01-10T12:00:00.000Z"
}
```

---

## 🔒 **Security & Permissions**

### **User Roles**
- `admin`: Full system access
- `manager`: Management operations
- `user`: Standard operations
- `viewer`: Read-only access

### **API Key Permissions**
- `shipments:create` - Create shipments
- `shipments:read` - Read shipment data
- `shipments:update` - Update shipments
- `shipments:delete` - Delete shipments
- `rates:calculate` - Calculate rates
- `tracking:read` - Read tracking info
- `webhooks:manage` - Manage webhooks
- `admin:*` - Administrative access

### **Rate Limiting**
- API keys: Configurable requests per window
- JWT tokens: Built-in expiration
- IP-based restrictions available

---

## 🧪 **Testing Examples**

### **Create Test User**
```bash
curl -X POST http://localhost:3000/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123",
    "firstName": "Test",
    "lastName": "User"
  }'
```

### **Login and Get Token**
```bash
curl -X POST http://localhost:3000/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "testuser",
    "password": "password123"
  }'
```

### **Create API Key**
```bash
curl -X POST http://localhost:3000/auth/api-keys \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "Test API Key",
    "permissions": ["shipments:create", "shipments:read"]
  }'
```

### **Test API with API Key**
```bash
curl -X GET http://localhost:3000/health \
  -H "X-API-Key: YOUR_API_KEY"
```

---

## 📋 **Error Codes**

| Status Code | Description |
|-------------|-------------|
| 200 | Success |
| 201 | Created |
| 400 | Bad Request |
| 401 | Unauthorized |
| 403 | Forbidden |
| 404 | Not Found |
| 409 | Conflict |
| 422 | Validation Error |
| 429 | Too Many Requests |
| 500 | Internal Server Error |
| 503 | Service Unavailable |

---

## 🔄 **Rate Limits**

- **API Keys**: Configurable per key (default: 100 requests/15min)
- **JWT Tokens**: Based on token expiration
- **Global Limits**: Additional IP-based rate limiting

---

## 📞 **Support**

For API support and questions:
- Check this documentation first
- Review the main README.md for setup instructions
- Create an issue in the project repository
- Contact the development team

---

**Last Updated:** January 10, 2024
**Version:** 1.0.0
