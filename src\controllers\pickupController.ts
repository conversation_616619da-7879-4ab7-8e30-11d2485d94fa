import { Request, Response } from 'express';
import { as<PERSON><PERSON><PERSON><PERSON>, sendError, sendSuccess } from '../helpers/apiResponse';
import logger from '../helpers/logger';
import {
  validateAddress,
  validateAgainstSchema,
  validateContact,
  validateNumber,
  validateObject,
  validateString,
  ValidationSchema
} from '../helpers/validation';
import { sanitizeRequestBody } from '../middleware/requestLogger';
import { IPickupRequest, ISimplifiedPickupRequest, pickupService } from '../services/shippingService';

// ==========================================
// PICKUP VALIDATION SCHEMA
// ==========================================

const pickupAddressSchema: ValidationSchema = {
  addressLine1: { type: 'string', required: true },
  addressLine2: { type: 'string', required: false },
  addressLine3: { type: 'string', required: false },
  city: { type: 'string', required: true },
  stateOrProvinceCode: { type: 'string', required: false },
  postCode: { type: 'string', required: false },
  countryCode: { type: 'string', required: true }
};

const pickupContactSchema: ValidationSchema = {
  department: { type: 'string', required: false },
  personName: { type: 'string', required: true },
  title: { type: 'string', required: false },
  companyName: { type: 'string', required: true },
  phoneNumber: { type: 'string', required: true },
  phoneNumberExt: { type: 'string', required: false },
  cellPhone: { type: 'string', required: false },
  emailAddress: { type: 'string', required: true }
};

// Custom validators for nested objects
const validateShipmentWeight = (value: any, fieldName: string) => {
  if (!value || typeof value !== 'object') {
    throw new Error(`${fieldName} must be an object`);
  }
  if (!value.unit || typeof value.unit !== 'string') {
    throw new Error(`${fieldName}.unit is required and must be a string`);
  }
  if (value.value === undefined || typeof value.value !== 'number') {
    throw new Error(`${fieldName}.value is required and must be a number`);
  }
};

const validateShipmentDimensions = (value: any, fieldName: string) => {
  if (!value || typeof value !== 'object') {
    throw new Error(`${fieldName} must be an object`);
  }
  if (value.length === undefined || typeof value.length !== 'number') {
    throw new Error(`${fieldName}.length is required and must be a number`);
  }
  if (value.width === undefined || typeof value.width !== 'number') {
    throw new Error(`${fieldName}.width is required and must be a number`);
  }
  if (value.height === undefined || typeof value.height !== 'number') {
    throw new Error(`${fieldName}.height is required and must be a number`);
  }
  if (value.unit !== undefined && typeof value.unit !== 'string') {
    throw new Error(`${fieldName}.unit must be a string if provided`);
  }
};

const validatePickupItems = (value: any, fieldName: string) => {
  if (!Array.isArray(value) || value.length === 0) {
    throw new Error(`${fieldName} must be a non-empty array`);
  }
  
  value.forEach((item, index) => {
    if (!item || typeof item !== 'object') {
      throw new Error(`${fieldName}[${index}] must be an object`);
    }
    
    // Validate required fields
    const requiredFields = [
      'productGroup', 'productType', 'numberOfShipments', 'packageType', 
      'payment', 'shipmentWeight', 'numberOfPieces', 'shipmentDimensions'
    ];
    
    requiredFields.forEach(field => {
      if (!item[field]) {
        throw new Error(`${fieldName}[${index}].${field} is required`);
      }
    });
    
    // Validate nested objects
    validateShipmentWeight(item.shipmentWeight, `${fieldName}[${index}].shipmentWeight`);
    validateShipmentDimensions(item.shipmentDimensions, `${fieldName}[${index}].shipmentDimensions`);
  });
};

const validateReferences = (value: any, fieldName: string) => {
  if (value && typeof value === 'object') {
    if (value.reference1 !== undefined && typeof value.reference1 !== 'string') {
      throw new Error(`${fieldName}.reference1 must be a string if provided`);
    }
    if (value.reference2 !== undefined && typeof value.reference2 !== 'string') {
      throw new Error(`${fieldName}.reference2 must be a string if provided`);
    }
  } else if (value !== undefined) {
    throw new Error(`${fieldName} must be an object if provided`);
  }
};

const pickupItemSchema: ValidationSchema = {
  productGroup: { type: 'string', required: true },
  productType: { type: 'string', required: true },
  numberOfShipments: { type: 'number', required: true },
  packageType: { type: 'string', required: true },
  payment: { type: 'string', required: true },
  shipmentWeight: { 
    type: 'object', 
    required: true,
    customValidator: validateShipmentWeight
  },
  shipmentVolume: { type: 'object', required: false },
  numberOfPieces: { type: 'number', required: true },
  cashAmount: { type: 'number', required: false },
  extraCharges: { type: 'object', required: false },
  shipmentDimensions: {
    type: 'object',
    required: true,
    customValidator: validateShipmentDimensions
  },
  comments: { type: 'string', required: false }
};

const createPickupSchema: ValidationSchema = {
  pickupAddress: { 
    type: 'object',
    customValidator: validateAddress, 
    required: true
  },
  pickupContact: { 
    type: 'object',
    customValidator: validateContact, 
    required: true
  },
  pickupLocation: { type: 'string', required: true },
  pickupDate: { type: 'string', required: true },
  readyTime: { type: 'string', required: true },
  lastPickupTime: { type: 'string', required: true },
  closingTime: { type: 'string', required: true },
  comments: { type: 'string', required: false },
  vehicle: { type: 'string', required: false },
  pickupItems: { 
    type: 'array',
    customValidator: validatePickupItems, 
    required: true
  },
  references: {
    type: 'object',
    customValidator: validateReferences,
    required: false
  }
};

export class PickupController {
  /**
   * Create a new pickup
   */
  public createPickup = asyncHandler(async (req: Request, res: Response) => {
    logger.info('Create pickup request received', {
      method: req.method,
      path: req.path,
      query: req.query,
      params: req.params,
      body: sanitizeRequestBody(req.body), // Log sanitized request data
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      requestId: (req as any).requestId
    });

    // Validate request
    validateAgainstSchema(req.body, createPickupSchema);

    // Log validation success
    logger.info('Request validation passed', {
      requestId: (req as any).requestId,
      validationFields: Object.keys(createPickupSchema)
    });

    // Additional business validation
    const validationResult = pickupService.validatePickupData(req.body as IPickupRequest);
    if (!validationResult.isValid) {
      logger.warn('Pickup data validation failed', {
        requestId: (req as any).requestId,
        errors: validationResult.errors
      });
      return sendError(res, 'Validation failed', 400);
    }

    try {
      // Create pickup
      const result = await pickupService.createPickup(req.body as IPickupRequest);

      if (result.success) {
        logger.info('Pickup created successfully', {
          requestId: (req as any).requestId,
          pickupGUID: result.pickupGUID,
          reference: result.reference
        });

        sendSuccess(res, result.message, {
          pickupGUID: result.pickupGUID,
          reference: result.reference,
          data: result.data
        });
      } else {
        logger.error('Pickup creation failed', {
          requestId: (req as any).requestId,
          error: result.message
        });
        sendError(res, result.message, 400);
      }
    } catch (error) {
      logger.error('Pickup controller error', {
        requestId: (req as any).requestId,
        error: error
      });
      sendError(res, 'Internal server error during pickup creation', 500);
    }
  });

  /**
   * Create a new pickup using simplified payload structure (matches Postman collection)
   */
  public createPickupSimplified = asyncHandler(async (req: Request, res: Response) => {
    logger.info('Create pickup (simplified) request received', {
      method: req.method,
      path: req.path,
      query: req.query,
      params: req.params,
      body: sanitizeRequestBody(req.body), // Log sanitized request data
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      requestId: (req as any).requestId
    });

    // Basic validation for required fields
    if (!req.body.PickupAddress || !req.body.PickupContact || !req.body.PickupItems) {
      logger.warn('Missing required fields in simplified pickup request', {
        requestId: (req as any).requestId,
        hasPickupAddress: !!req.body.PickupAddress,
        hasPickupContact: !!req.body.PickupContact,
        hasPickupItems: !!req.body.PickupItems
      });
      return sendError(res, 'Missing required fields: PickupAddress, PickupContact, and PickupItems are required', 400);
    }

    try {
      // Create pickup using simplified request
      const result = await pickupService.createPickupFromSimplified(req.body as ISimplifiedPickupRequest);

      if (result.success) {
        logger.info('Simplified pickup created successfully', {
          requestId: (req as any).requestId,
          pickupGUID: result.pickupGUID,
          reference: result.reference
        });

        sendSuccess(res, result.message, {
          pickupGUID: result.pickupGUID,
          reference: result.reference,
          data: result.data
        });
      } else {
        logger.error('Simplified pickup creation failed', {
          requestId: (req as any).requestId,
          error: result.message
        });
        sendError(res, result.message, 400);
      }
    } catch (error) {
      logger.error('Simplified pickup controller error', {
        requestId: (req as any).requestId,
        error: error
      });
      sendError(res, 'Internal server error during simplified pickup creation', 500);
    }
  });
}

export const pickupController = new PickupController();