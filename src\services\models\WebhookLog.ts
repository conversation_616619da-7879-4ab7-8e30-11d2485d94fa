// models/WebhookLog.ts
import mongoose, { Schema, Document } from "mongoose";

export interface IWebhookLog extends Document {
  receivedAt: Date;
  payload: any;
}

const WebhookLogSchema = new Schema<IWebhookLog>({
  receivedAt: { type: Date, default: Date.now },
  payload: { type: Schema.Types.Mixed, required: true },
});

export default mongoose.model<IWebhookLog>("WebhookLog", WebhookLogSchema);
