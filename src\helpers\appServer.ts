import express = require("express");
import { Application } from "express";

class App {
  public app: Application;
  public port: number;

  constructor(appInit: {
    port: number;
    defaults: any;
    middleWares: any;
    routes: any;
  }) {
    console.log("Application Initialization Started");
    this.app = express();
    this.port = appInit.port;
    this.setDefaults(appInit.defaults);
    this.middlewares(appInit.middleWares);
    this.routes(appInit.routes);
  }
  private setDefaults(middleWares: {
    forEach: (arg0: (middleWare: any) => void) => void;
  }) {
    console.log("Application Setting Defaults");
    middleWares.forEach((middleWare) => {
      this.app.use(middleWare);
    });
  }
  private middlewares(middleWares: {
    forEach: (arg0: (middleWare: any) => void) => void;
  }) {
    console.log("Application Setting Middlewares");
    middleWares.forEach((middleWare) => {
      this.app.use(middleWare);
    });
  }

  private routes(controllers: {
    forEach: (arg0: (controller: any) => void) => void;
  }) {
    console.log("Application Setting Routes");
    controllers.forEach((controller) => {
      this.app.use("/", controller.router);
    });
  }

  public listen() {
    this.app.listen(this.port, () => {
      console.log(
        `Application listening on the port : http://localhost:${this.port}`
      );
    });
  }
}
export default App;
