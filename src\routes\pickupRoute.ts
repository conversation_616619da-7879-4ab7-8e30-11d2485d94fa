import * as express from "express";
import { pickupController } from "../controllers/pickupController";

class PickupRoute {
  public router = express.Router();

  constructor() {
    this.initRoutes();
  }

  public initRoutes() {
    // Create pickup endpoint (standard format)
    this.router.post("/create-pickup", pickupController.createPickup);

    // Create pickup endpoint (simplified format - matches Postman collection structure)
    this.router.post("/create-pickup-simplified", pickupController.createPickupSimplified);
  }
}

export default PickupRoute;
