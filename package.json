{"name": "delivery-microservice", "version": "1.0.0", "description": "A comprehensive delivery microservice built with Node.js, Express, TypeScript, and MongoDB", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/src/server.js", "dev": "ts-node-dev --respawn --transpile-only src/server.ts", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "validate-setup": "./validate-setup.sh", "docker:build": "docker build -t batayeq-delivery .", "docker:run": "docker run -p 3000:3000 batayeq-delivery"}, "engines": {"node": "v22.14.0", "npm": "10.9.2"}, "author": "Batayeq Team", "license": "MIT", "dependencies": {"@types/bcrypt": "^6.0.0", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.10", "@types/mongoose": "^5.11.97", "axios": "^1.7.7", "bcrypt": "^6.0.0", "body-parser": "^1.20.3", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.0", "jsonwebtoken": "^9.0.2", "mongoose": "^7.6.3"}, "devDependencies": {"@types/node": "14.18.3", "supertest": "^6.3.4", "ts-node": "^10.9.2", "typescript": "^4.9.5"}}