import config from '../config/appConfig';
import { ApiError } from '../helpers/apiResponse';
import { generateId } from '../helpers/httpClient';
import logger from '../helpers/logger';
import { aramexService } from './aramexService';

export interface IShippingRequest {
  shipper: {
    addressLine1: string;
    addressLine2?: string;
    city: string;
    stateOrProvinceCode?: string;
    postCode: string;
    countryCode: string;
    personName: string;
    phoneNumber: string;
    cellPhone?: string;
    emailAddress: string;
    companyName: string;
  };
  consignee: {
    addressLine1: string;
    addressLine2?: string;
    city: string;
    stateOrProvinceCode?: string;
    postCode: string;
    countryCode: string;
    personName: string;
    phoneNumber: string;
    cellPhone?: string;
    emailAddress: string;
    companyName: string;
  };
  shipmentDetails: {
    weight: number;
    dimensions: {
      length: number;
      width: number;
      height: number;
    };
    numberOfPieces: number;
    internalOrderId?: string;
    customerCharges?: number;
  };
  references?: {
    reference1?: string;
    reference2?: string;
    reference3?: string;
  };
}

export interface IParty {
  Reference1: string | null;
  Reference2: string | null;
  AccountNumber: string;
  PartyAddress: {
    Line1: string;
    Line2: string | null;
    Line3: string;
    City: string;
    StateOrProvinceCode: string | null;
    PostCode: string;
    CountryCode: string;
    Longitude: number;
    Latitude: number;
    BuildingNumber: string | null;
    BuildingName: string | null;
    Floor: string | null;
    Apartment: string | null;
    POBox: string | null;
    Description: string | null;
  };
  Contact: {
    Department: string | null;
    PersonName: string;
    Title: string | null;
    CompanyName: string;
    PhoneNumber1: string;
    PhoneNumber1Ext: string;
    PhoneNumber2: string;
    FaxNumber: string | null;
    CellPhone: string | null;
    EmailAddress: string;
    Type: string;
  };
}

export interface IShippingResponse {
  success: boolean;
  message: string;
  data?: any;
  trackingNumber?: string;
  reference?: string;
}

export class ShippingService {
  private static instance: ShippingService;

  public static getInstance(): ShippingService {
    if (!ShippingService.instance) {
      ShippingService.instance = new ShippingService();
    }
    return ShippingService.instance;
  }

  /**
   * Create a new shipment
   */
  public async createShipment(request: IShippingRequest): Promise<IShippingResponse> {
    try {
      logger.info('Creating new shipment', {
        shipper: request.shipper.companyName,
        consignee: request.consignee.companyName,
        weight: request.shipmentDetails.weight,
        pieces: request.shipmentDetails.numberOfPieces
      });

      // Build party objects
      const shipper = this.buildParty(request.shipper, true);
      const consignee = this.buildParty(request.consignee, false);
      const thirdParty = this.buildThirdParty(shipper, request);

      // Build Aramex shipment
      const aramexShipment = aramexService.buildAramexShipment(
        shipper,
        consignee,
        thirdParty,
        request.shipmentDetails,
        request.references
      );

      // Create shipment via Aramex
      const result = await aramexService.createShipment([aramexShipment]);

      if (result.success) {
        logger.info('Shipment created successfully', {
          reference: result.reference,
          trackingNumber: result.trackingNumber
        });

        return {
          success: true,
          message: 'Shipment created successfully',
          data: result.data,
          trackingNumber: result.trackingNumber,
          reference: result.reference
        };
      } else {
        logger.error('Shipment creation failed', { error: result.message });
        return {
          success: false,
          message: result.message || 'Failed to create shipment'
        };
      }
    } catch (error) {
      logger.error('Shipping service error', error);
      throw new ApiError('Failed to create shipment', 500);
    }
  }

  /**
   * Build party object from request data
   */
  private buildParty(data: any, isShipper: boolean = true): IParty {
    return {
      Reference1: data.references?.reference1 || null,
      Reference2: data.references?.reference2 || null,
      AccountNumber: '',
      PartyAddress: {
        Line1: data.addressLine1,
        Line2: data.addressLine2 || null,
        Line3: data.addressLine3 || null,
        City: data.city,
        StateOrProvinceCode: data.stateOrProvinceCode || null,
        PostCode: data.postCode,
        CountryCode: data.countryCode,
        Longitude: 0,
        Latitude: 0,
        BuildingNumber: null,
        BuildingName: null,
        Floor: null,
        Apartment: null,
        POBox: null,
        Description: null,
      },
      Contact: {
        Department: null,
        PersonName: data.personName,
        Title: null,
        CompanyName: data.companyName,
        PhoneNumber1: data.phoneNumber,
        PhoneNumber1Ext: "",
        PhoneNumber2: "",
        FaxNumber: null,
        CellPhone: data.cellPhone || data.phoneNumber,
        EmailAddress: data.emailAddress,
        Type: "",
      },
    };
  }

  /**
   * Build third party object for billing
   */
  private buildThirdParty(shipper: IParty, request: IShippingRequest): IParty {
    return {
      Reference1: request.references?.reference1 || `Internal Order: ${request.shipmentDetails.internalOrderId || generateId('ORD')}`,
      Reference2: request.references?.reference2 || `Customer Charges: ${request.shipmentDetails.customerCharges || 0}`,
      AccountNumber: config.aramex.adminAccountNumber,
      PartyAddress: {
        Line1: shipper.PartyAddress.Line1,
        Line2: shipper.PartyAddress.Line2,
        Line3: shipper.PartyAddress.Line3,
        City: shipper.PartyAddress.City,
        StateOrProvinceCode: shipper.PartyAddress.StateOrProvinceCode,
        PostCode: shipper.PartyAddress.PostCode,
        CountryCode: shipper.PartyAddress.CountryCode,
        Longitude: 0,
        Latitude: 0,
        BuildingNumber: null,
        BuildingName: null,
        Floor: null,
        Apartment: null,
        POBox: null,
        Description: null,
      },
      Contact: {
        Department: "Billing Department",
        PersonName: "App Admin",
        Title: "Billing Manager",
        CompanyName: "Your Company Name",
        PhoneNumber1: "ADMIN_PHONE", // Use config
        PhoneNumber1Ext: "",
        PhoneNumber2: "",
        FaxNumber: null,
        CellPhone: "*********",
        EmailAddress: "ADMIN_EMAIL", // Use config
        Type: "",
      },
    };
  }

  /**
   * Calculate shipment cost estimate
   */
  public async calculateShippingCost(request: IShippingRequest): Promise<{
    success: boolean;
    cost?: number;
    currency?: string;
    message: string;
  }> {
    try {
      // This would typically call Aramex rate calculator
      // For now, return a placeholder
      logger.info('Calculating shipping cost', {
        origin: request.shipper.city,
        destination: request.consignee.city,
        weight: request.shipmentDetails.weight
      });

      // Placeholder calculation - replace with actual Aramex rate calculation
      const baseRate = 50; // Base rate
      const weightMultiplier = request.shipmentDetails.weight * 2;
      const distanceMultiplier = 1.5; // Based on distance
      const estimatedCost = (baseRate + weightMultiplier) * distanceMultiplier;

      return {
        success: true,
        cost: Math.round(estimatedCost * 100) / 100,
        currency: 'AED',
        message: 'Cost calculated successfully'
      };
    } catch (error) {
      logger.error('Cost calculation error', error);
      return {
        success: false,
        message: 'Failed to calculate shipping cost'
      };
    }
  }

  /**
   * Validate shipment data
   */
  public validateShipmentData(data: IShippingRequest): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate shipper
    if (!data.shipper?.addressLine1) errors.push('Shipper address line 1 is required');
    if (!data.shipper?.city) errors.push('Shipper city is required');
    if (!data.shipper?.countryCode) errors.push('Shipper country code is required');
    if (!data.shipper?.personName) errors.push('Shipper person name is required');
    if (!data.shipper?.phoneNumber) errors.push('Shipper phone number is required');

    // Validate consignee
    if (!data.consignee?.addressLine1) errors.push('Consignee address line 1 is required');
    if (!data.consignee?.city) errors.push('Consignee city is required');
    if (!data.consignee?.countryCode) errors.push('Consignee country code is required');
    if (!data.consignee?.personName) errors.push('Consignee person name is required');
    if (!data.consignee?.phoneNumber) errors.push('Consignee phone number is required');

    // Validate shipment details
    if (!data.shipmentDetails?.weight || data.shipmentDetails.weight <= 0) {
      errors.push('Valid shipment weight is required');
    }
    if (!data.shipmentDetails?.dimensions?.length || data.shipmentDetails.dimensions.length <= 0) {
      errors.push('Valid shipment length is required');
    }
    if (!data.shipmentDetails?.dimensions?.width || data.shipmentDetails.dimensions.width <= 0) {
      errors.push('Valid shipment width is required');
    }
    if (!data.shipmentDetails?.dimensions?.height || data.shipmentDetails.dimensions.height <= 0) {
      errors.push('Valid shipment height is required');
    }
    if (!data.shipmentDetails?.numberOfPieces || data.shipmentDetails.numberOfPieces <= 0) {
      errors.push('Valid number of pieces is required');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

// ==========================================
// PICKUP INTERFACES
// ==========================================

export interface IPickupRequest {
  pickupAddress: {
    addressLine1: string;
    addressLine2?: string;
    addressLine3?: string;
    city: string;
    stateOrProvinceCode?: string;
    postCode?: string;
    countryCode: string;
  };
  pickupContact: {
    department?: string;
    personName: string;
    title?: string;
    companyName: string;
    phoneNumber: string;
    phoneNumberExt?: string;
    cellPhone?: string;
    emailAddress: string;
  };
  pickupLocation: string;
  pickupDate: string; // ISO date string
  readyTime: string; // ISO date string
  lastPickupTime: string; // ISO date string
  closingTime: string; // ISO date string
  comments?: string;
  vehicle?: string;
  pickupItems: IPickupItem[];
  references?: {
    reference1?: string;
    reference2?: string;
  };
}

// Simplified pickup request interface that matches the payload structure from Postman collection
export interface ISimplifiedPickupRequest {
  PickupAddress: {
    Line1: string;
    Line2?: string;
    Line3?: string;
    City: string;
    StateOrProvinceCode?: string;
    PostCode?: string;
    CountryCode: string;
  };
  PickupContact: {
    EmailAddress: string;
    CellPhone: string;
    PersonName?: string;
    CompanyName?: string;
    PhoneNumber1?: string;
    Department?: string;
  };
  PickupLocation: string;
  PickupDate: string;
  ReadyTime: string;
  LastPickupTime: string;
  ClosingTime: string;
  PickupItems: ISimplifiedPickupItem[];
  Status?: string;
  ExistingShipments?: any;
  Comments?: string;
  Vehicle?: string;
  References?: {
    Reference1?: string;
    Reference2?: string;
  };
}

export interface ISimplifiedPickupItem {
  ProductGroup: string;
  ProductType: string;
  NumberOfShipments: number;
  PackageType: string;
  Payment: string;
  ShipmentWeight: {
    Unit: string;
    Value: number;
  };
  ShipmentVolume?: any;
  NumberOfPieces: number;
  CashAmount?: number;
  ExtraCharges?: any;
  ShipmentDimensions: {
    Length: number;
    Width: number;
    Height: number;
    Unit: string;
  };
  Comments?: string;
}

export interface IPickupItem {
  productGroup: string; // e.g., "DOM"
  productType: string; // e.g., "ONP"
  numberOfShipments: number;
  packageType: string; // e.g., "Box"
  payment: string; // e.g., "P"
  shipmentWeight: {
    unit: string; // e.g., "KG"
    value: number;
  };
  shipmentVolume?: any;
  numberOfPieces: number;
  cashAmount?: number;
  extraCharges?: any;
  shipmentDimensions: {
    length: number;
    width: number;
    height: number;
    unit: string;
  };
  comments?: string;
}

export interface IPickupResponse {
  success: boolean;
  message: string;
  data?: any;
  pickupGUID?: string;
  reference?: string;
}

export interface IAramexPickupPayload {
  ClientInfo: any;
  Pickup: {
    PickupAddress: any;
    PickupContact: any;
    PickupLocation: string;
    PickupDate: string;
    ReadyTime: string;
    LastPickupTime: string;
    ClosingTime: string;
    Comments?: string;
    Reference1?: string;
    Reference2?: string;
    Vehicle?: string;
    Shipments?: any;
    PickupItems: any[];
    Status: string;
    ExistingShipments?: any;
    Branch?: string;
    RouteCode?: string;
  };
  Transaction: {
    Reference1?: string;
    Reference2?: string;
    Reference3?: string;
    Reference4?: string;
    Reference5?: string;
  };
}

// ==========================================
// PICKUP SERVICE
// ==========================================

export class PickupService {
  private static instance: PickupService;

  public static getInstance(): PickupService {
    if (!PickupService.instance) {
      PickupService.instance = new PickupService();
    }
    return PickupService.instance;
  }

  /**
   * Create a new pickup
   */
  public async createPickup(request: IPickupRequest): Promise<IPickupResponse> {
    try {
      logger.info('Creating new pickup', {
        pickupLocation: request.pickupLocation,
        pickupDate: request.pickupDate,
        itemsCount: request.pickupItems.length,
        companyName: request.pickupContact.companyName
      });

      // Build Aramex pickup payload
      const aramexPickupPayload = aramexService.buildAramexPickupPayload(
        request.pickupAddress,
        request.pickupContact,
        request.pickupLocation,
        this.formatDateForAramex(request.pickupDate),
        this.formatDateForAramex(request.readyTime),
        this.formatDateForAramex(request.lastPickupTime),
        this.formatDateForAramex(request.closingTime),
        request.pickupItems,
        request.references,
        request.comments,
        request.vehicle
      );

      // Create pickup via Aramex
      const result = await aramexService.createPickup(aramexPickupPayload);

      if (result.success) {
        logger.info('Pickup created successfully', {
          reference: result.reference,
          pickupGUID: result.pickupGUID
        });

        return {
          success: true,
          message: 'Pickup created successfully',
          data: result.data,
          pickupGUID: result.pickupGUID,
          reference: result.reference
        };
      } else {
        logger.error('Pickup creation failed', { error: result.message });
        return {
          success: false,
          message: result.message || 'Failed to create pickup'
        };
      }
    } catch (error) {
      logger.error('Pickup service error', error);
      throw new ApiError('Failed to create pickup', 500);
    }
  }

  /**
   * Create a new pickup from simplified request (matches Postman collection structure)
   */
  public async createPickupFromSimplified(request: ISimplifiedPickupRequest): Promise<IPickupResponse> {
    try {
      logger.info('Creating new pickup from simplified request', {
        pickupLocation: request.PickupLocation,
        pickupDate: request.PickupDate,
        itemsCount: request.PickupItems.length,
        companyName: request.PickupContact.CompanyName
      });

      // Validate and sanitize the simplified request
      const validationResult = this.validateSimplifiedPickupData(request);
      if (!validationResult.isValid) {
        logger.warn('Simplified pickup data validation failed', {
          errors: validationResult.errors
        });
        return {
          success: false,
          message: 'Validation failed: ' + validationResult.errors.join(', ')
        };
      }

      // Transform simplified request to standard format
      const standardRequest = this.transformSimplifiedToStandard(request);

      // Use the standard createPickup method
      return await this.createPickup(standardRequest);
    } catch (error) {
      logger.error('Simplified pickup service error', error);
      throw new ApiError('Failed to create pickup from simplified request', 500);
    }
  }

  /**
   * Transform simplified pickup request to standard format
   */
  private transformSimplifiedToStandard(simplified: ISimplifiedPickupRequest): IPickupRequest {
    // Build pickup contact with proper defaults
    const pickupContact = this.buildPickupContactFromSimplified(simplified.PickupContact);

    // Transform pickup items
    const pickupItems = simplified.PickupItems.map(item => this.transformSimplifiedPickupItem(item));

    return {
      pickupAddress: {
        addressLine1: simplified.PickupAddress.Line1,
        addressLine2: simplified.PickupAddress.Line2,
        addressLine3: simplified.PickupAddress.Line3,
        city: simplified.PickupAddress.City,
        stateOrProvinceCode: simplified.PickupAddress.StateOrProvinceCode,
        postCode: simplified.PickupAddress.PostCode || '',
        countryCode: simplified.PickupAddress.CountryCode
      },
      pickupContact,
      pickupLocation: simplified.PickupLocation,
      pickupDate: simplified.PickupDate,
      readyTime: simplified.ReadyTime,
      lastPickupTime: simplified.LastPickupTime,
      closingTime: simplified.ClosingTime,
      comments: simplified.Comments,
      vehicle: simplified.Vehicle,
      pickupItems,
      references: {
        reference1: simplified.References?.Reference1,
        reference2: simplified.References?.Reference2
      }
    };
  }

  /**
   * Build pickup contact from simplified contact data
   */
  private buildPickupContactFromSimplified(contact: ISimplifiedPickupRequest['PickupContact']) {
    return {
      department: contact.Department || 'General',
      personName: contact.PersonName || 'Contact Person',
      title: 'Contact',
      companyName: contact.CompanyName || 'Company',
      phoneNumber: contact.PhoneNumber1 || contact.CellPhone,
      phoneNumberExt: '',
      cellPhone: contact.CellPhone,
      emailAddress: contact.EmailAddress
    };
  }

  /**
   * Transform simplified pickup item to standard format
   */
  private transformSimplifiedPickupItem(item: ISimplifiedPickupItem): IPickupItem {
    return {
      productGroup: item.ProductGroup,
      productType: item.ProductType,
      numberOfShipments: item.NumberOfShipments,
      packageType: item.PackageType,
      payment: item.Payment,
      shipmentWeight: {
        unit: item.ShipmentWeight.Unit,
        value: item.ShipmentWeight.Value
      },
      shipmentVolume: item.ShipmentVolume,
      numberOfPieces: item.NumberOfPieces,
      cashAmount: item.CashAmount,
      extraCharges: item.ExtraCharges,
      shipmentDimensions: {
        length: item.ShipmentDimensions.Length,
        width: item.ShipmentDimensions.Width,
        height: item.ShipmentDimensions.Height,
        unit: item.ShipmentDimensions.Unit
      },
      comments: item.Comments
    };
  }

  /**
   * Format date for Aramex API (Microsoft JSON date format)
   */
  private formatDateForAramex(dateString: string): string {
    const date = new Date(dateString);
    const timestamp = date.getTime();
    return `/Date(${timestamp})/`;
  }

  /**
   * Validate pickup data
   */
  public validatePickupData(data: IPickupRequest): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate pickup address
    if (!data.pickupAddress?.addressLine1) errors.push('Pickup address line 1 is required');
    if (!data.pickupAddress?.city) errors.push('Pickup city is required');
    if (!data.pickupAddress?.countryCode) errors.push('Pickup country code is required');

    // Validate pickup contact
    if (!data.pickupContact?.personName) errors.push('Pickup contact person name is required');
    if (!data.pickupContact?.companyName) errors.push('Pickup contact company name is required');
    if (!data.pickupContact?.phoneNumber) errors.push('Pickup contact phone number is required');
    if (!data.pickupContact?.emailAddress) errors.push('Pickup contact email address is required');

    // Validate pickup details
    if (!data.pickupLocation) errors.push('Pickup location is required');
    if (!data.pickupDate) errors.push('Pickup date is required');
    if (!data.readyTime) errors.push('Ready time is required');
    if (!data.lastPickupTime) errors.push('Last pickup time is required');
    if (!data.closingTime) errors.push('Closing time is required');

    // Validate pickup items
    if (!data.pickupItems || data.pickupItems.length === 0) {
      errors.push('At least one pickup item is required');
    } else {
      data.pickupItems.forEach((item, index) => {
        if (!item.productGroup) errors.push(`Pickup item ${index + 1}: Product group is required`);
        if (!item.productType) errors.push(`Pickup item ${index + 1}: Product type is required`);
        if (!item.numberOfShipments || item.numberOfShipments <= 0) {
          errors.push(`Pickup item ${index + 1}: Valid number of shipments is required`);
        }
        if (!item.packageType) errors.push(`Pickup item ${index + 1}: Package type is required`);
        if (!item.payment) errors.push(`Pickup item ${index + 1}: Payment type is required`);
        if (!item.shipmentWeight?.value || item.shipmentWeight.value <= 0) {
          errors.push(`Pickup item ${index + 1}: Valid shipment weight is required`);
        }
        if (!item.shipmentWeight?.unit) {
          errors.push(`Pickup item ${index + 1}: Shipment weight unit is required`);
        }
        if (!item.numberOfPieces || item.numberOfPieces <= 0) {
          errors.push(`Pickup item ${index + 1}: Valid number of pieces is required`);
        }
      });
    }

    // Validate dates
    try {
      const pickupDate = new Date(data.pickupDate);
      const readyTime = new Date(data.readyTime);
      const lastPickupTime = new Date(data.lastPickupTime);
      const closingTime = new Date(data.closingTime);

      if (isNaN(pickupDate.getTime())) errors.push('Invalid pickup date format');
      if (isNaN(readyTime.getTime())) errors.push('Invalid ready time format');
      if (isNaN(lastPickupTime.getTime())) errors.push('Invalid last pickup time format');
      if (isNaN(closingTime.getTime())) errors.push('Invalid closing time format');

      // Check logical date order
      if (readyTime >= lastPickupTime) {
        errors.push('Ready time must be before last pickup time');
      }
      if (lastPickupTime >= closingTime) {
        errors.push('Last pickup time must be before closing time');
      }
    } catch (error) {
      errors.push('Invalid date format provided');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate simplified pickup data
   */
  public validateSimplifiedPickupData(data: ISimplifiedPickupRequest): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate pickup address
    if (!data.PickupAddress?.Line1) errors.push('Pickup address Line1 is required');
    if (!data.PickupAddress?.City) errors.push('Pickup city is required');
    if (!data.PickupAddress?.CountryCode) errors.push('Pickup country code is required');

    // Validate pickup contact
    if (!data.PickupContact?.EmailAddress) errors.push('Pickup contact email address is required');
    if (!data.PickupContact?.CellPhone) errors.push('Pickup contact cell phone is required');

    // Validate pickup details
    if (!data.PickupLocation) errors.push('Pickup location is required');
    if (!data.PickupDate) errors.push('Pickup date is required');
    if (!data.ReadyTime) errors.push('Ready time is required');
    if (!data.LastPickupTime) errors.push('Last pickup time is required');
    if (!data.ClosingTime) errors.push('Closing time is required');

    // Validate pickup items
    if (!data.PickupItems || data.PickupItems.length === 0) {
      errors.push('At least one pickup item is required');
    } else {
      data.PickupItems.forEach((item, index) => {
        if (!item.ProductGroup) errors.push(`Pickup item ${index + 1}: Product group is required`);
        if (!item.ProductType) errors.push(`Pickup item ${index + 1}: Product type is required`);
        if (!item.NumberOfShipments || item.NumberOfShipments <= 0) {
          errors.push(`Pickup item ${index + 1}: Valid number of shipments is required`);
        }
        if (!item.PackageType) errors.push(`Pickup item ${index + 1}: Package type is required`);
        if (!item.Payment) errors.push(`Pickup item ${index + 1}: Payment type is required`);
        if (!item.ShipmentWeight?.Value || item.ShipmentWeight.Value <= 0) {
          errors.push(`Pickup item ${index + 1}: Valid shipment weight is required`);
        }
        if (!item.ShipmentWeight?.Unit) {
          errors.push(`Pickup item ${index + 1}: Shipment weight unit is required`);
        }
        if (!item.NumberOfPieces || item.NumberOfPieces <= 0) {
          errors.push(`Pickup item ${index + 1}: Valid number of pieces is required`);
        }
      });
    }

    // Validate dates (basic format check)
    try {
      if (data.PickupDate && isNaN(new Date(data.PickupDate).getTime())) {
        errors.push('Invalid pickup date format');
      }
      if (data.ReadyTime && isNaN(new Date(data.ReadyTime).getTime())) {
        errors.push('Invalid ready time format');
      }
      if (data.LastPickupTime && isNaN(new Date(data.LastPickupTime).getTime())) {
        errors.push('Invalid last pickup time format');
      }
      if (data.ClosingTime && isNaN(new Date(data.ClosingTime).getTime())) {
        errors.push('Invalid closing time format');
      }
    } catch (error) {
      errors.push('Invalid date format provided');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

export const shippingService = ShippingService.getInstance();
export const pickupService = PickupService.getInstance();
