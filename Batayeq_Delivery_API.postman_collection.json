{"info": {"name": "Batayeq Delivery Microservice API", "description": "Complete API collection for testing the Batayeq Delivery Microservice", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "1.0.0"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:3000", "type": "string"}, {"key": "access_token", "value": "", "type": "string"}, {"key": "refresh_token", "value": "", "type": "string"}, {"key": "api_key", "value": "", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testuser\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"firstName\": \"Test\",\n  \"lastName\": \"User\",\n  \"role\": \"user\"\n}"}, "url": {"raw": "{{base_url}}/auth/register", "host": ["{{base_url}}"], "path": ["auth", "register"]}}}, {"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('access_token', response.data.tokens.accessToken);", "    pm.collectionVariables.set('refresh_token', response.data.tokens.refreshToken);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"identifier\": \"testuser\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/refresh", "host": ["{{base_url}}"], "path": ["auth", "refresh"]}}}, {"name": "Get User Profile", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/auth/profile", "host": ["{{base_url}}"], "path": ["auth", "profile"]}}}, {"name": "Create API Key", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('api_key', response.data.apiKey.key);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"My Test API Key\",\n  \"description\": \"API key for testing\",\n  \"permissions\": [\"shipments:create\", \"shipments:read\"],\n  \"expiresAt\": \"2024-12-31T23:59:59.000Z\",\n  \"allowedIPs\": [\"*\"]\n}"}, "url": {"raw": "{{base_url}}/auth/api-keys", "host": ["{{base_url}}"], "path": ["auth", "api-keys"]}}}, {"name": "List API Keys", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/auth/api-keys", "host": ["{{base_url}}"], "path": ["auth", "api-keys"]}}}]}, {"name": "Shipping", "item": [{"name": "Create Shipment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"shipper\": {\n    \"addressLine1\": \"123 Shipper Street\",\n    \"addressLine2\": \"Building A, Floor 5\",\n    \"city\": \"Dubai\",\n    \"stateOrProvinceCode\": \"DU\",\n    \"postCode\": \"12345\",\n    \"countryCode\": \"AE\",\n    \"personName\": \"<PERSON>\",\n    \"phoneNumber\": \"+971501234567\",\n    \"cellPhone\": \"+971501234567\",\n    \"emailAddress\": \"<EMAIL>\",\n    \"companyName\": \"Shipper Corporation\"\n  },\n  \"consignee\": {\n    \"addressLine1\": \"456 Receiver Avenue\",\n    \"addressLine2\": \"Suite 100\",\n    \"city\": \"Abu Dhabi\",\n    \"stateOrProvinceCode\": \"AD\",\n    \"postCode\": \"67890\",\n    \"countryCode\": \"AE\",\n    \"personName\": \"<PERSON>\",\n    \"phoneNumber\": \"+971507654321\",\n    \"cellPhone\": \"+971507654321\",\n    \"emailAddress\": \"<EMAIL>\",\n    \"companyName\": \"Receiver Corporation\"\n  },\n  \"shipmentDetails\": {\n    \"weight\": 2.5,\n    \"dimensions\": {\n      \"length\": 30,\n      \"width\": 20,\n      \"height\": 15\n    },\n    \"numberOfPieces\": 1,\n    \"internalOrderId\": \"ORD-12345\",\n    \"customerCharges\": 150.00\n  },\n  \"references\": {\n    \"reference1\": \"REF-001\",\n    \"reference2\": \"Customer Order #123\",\n    \"reference3\": \"Priority Shipment\"\n  }\n}"}, "url": {"raw": "{{base_url}}/create-shipping", "host": ["{{base_url}}"], "path": ["create-shipping"]}}}, {"name": "Calculate Rate", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"OriginAddress\": {\n    \"Line1\": \"123 Origin Street\",\n    \"City\": \"Dubai\",\n    \"CountryCode\": \"AE\"\n  },\n  \"DestinationAddress\": {\n    \"Line1\": \"456 Destination Avenue\",\n    \"City\": \"Abu Dhabi\",\n    \"CountryCode\": \"AE\"\n  },\n  \"ShipmentDetails\": {\n    \"ActualWeight\": 2.5,\n    \"ChargeableWeight\": 3.0\n  },\n  \"PreferredCurrencyCode\": \"AED\"\n}"}, "url": {"raw": "{{base_url}}/delivery-rate", "host": ["{{base_url}}"], "path": ["delivery-rate"]}}}, {"name": "Get Tracking Info", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/tracking/TRACKING123", "host": ["{{base_url}}"], "path": ["tracking", "TRACKING123"]}}}]}, {"name": "Webhooks", "item": [{"name": "Receive Webhook", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"orderId\": \"ORD-12345\",\n  \"status\": \"confirmed\",\n  \"timestamp\": \"2024-01-10T10:00:00.000Z\",\n  \"customer\": {\n    \"name\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\"\n  },\n  \"items\": [\n    {\n      \"productId\": \"PROD-001\",\n      \"quantity\": 2,\n      \"price\": 50.00\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/webhook", "host": ["{{base_url}}"], "path": ["webhook"]}}}, {"name": "Get Webhook Logs", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/webhooks/logs?page=1&limit=10", "host": ["{{base_url}}"], "path": ["webhooks", "logs"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Get Webhook Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/webhooks/stats", "host": ["{{base_url}}"], "path": ["webhooks", "stats"]}}}]}, {"name": "Health & Monitoring", "item": [{"name": "Health Check (Ping)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/ping", "host": ["{{base_url}}"], "path": ["ping"]}}}, {"name": "Detailed Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}}}, {"name": "Service Information", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/info", "host": ["{{base_url}}"], "path": ["info"]}}}]}, {"name": "API Key Authentication Examples", "item": [{"name": "Create Shipment with API Key (Header)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Key", "value": "{{api_key}}"}], "body": {"mode": "raw", "raw": "{\n  \"shipper\": {\n    \"addressLine1\": \"123 Shipper Street\",\n    \"city\": \"Dubai\",\n    \"countryCode\": \"AE\",\n    \"personName\": \"<PERSON>\",\n    \"phoneNumber\": \"+971501234567\",\n    \"emailAddress\": \"<EMAIL>\",\n    \"companyName\": \"Shipper Corporation\"\n  },\n  \"consignee\": {\n    \"addressLine1\": \"456 Receiver Avenue\",\n    \"city\": \"Abu Dhabi\",\n    \"countryCode\": \"AE\",\n    \"personName\": \"<PERSON> Smith\",\n    \"phoneNumber\": \"+971507654321\",\n    \"emailAddress\": \"<EMAIL>\",\n    \"companyName\": \"Receiver Corporation\"\n  },\n  \"shipmentDetails\": {\n    \"weight\": 2.5,\n    \"dimensions\": {\n      \"length\": 30,\n      \"width\": 20,\n      \"height\": 15\n    },\n    \"numberOfPieces\": 1\n  }\n}"}, "url": {"raw": "{{base_url}}/create-shipping", "host": ["{{base_url}}"], "path": ["create-shipping"]}}}, {"name": "Create Shipment with API Key (Query)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"shipper\": {\n    \"addressLine1\": \"123 Shipper Street\",\n    \"city\": \"Dubai\",\n    \"countryCode\": \"AE\",\n    \"personName\": \"<PERSON>\",\n    \"phoneNumber\": \"+971501234567\",\n    \"emailAddress\": \"<EMAIL>\",\n    \"companyName\": \"Shipper Corporation\"\n  },\n  \"consignee\": {\n    \"addressLine1\": \"456 Receiver Avenue\",\n    \"city\": \"Abu Dhabi\",\n    \"countryCode\": \"AE\",\n    \"personName\": \"<PERSON> Smith\",\n    \"phoneNumber\": \"+971507654321\",\n    \"emailAddress\": \"<EMAIL>\",\n    \"companyName\": \"Receiver Corporation\"\n  },\n  \"shipmentDetails\": {\n    \"weight\": 2.5,\n    \"dimensions\": {\n      \"length\": 30,\n      \"width\": 20,\n      \"height\": 15\n    },\n    \"numberOfPieces\": 1\n  }\n}"}, "url": {"raw": "{{base_url}}/create-shipping?api_key={{api_key}}", "host": ["{{base_url}}"], "path": ["create-shipping"], "query": [{"key": "api_key", "value": "{{api_key}}"}]}}}]}]}