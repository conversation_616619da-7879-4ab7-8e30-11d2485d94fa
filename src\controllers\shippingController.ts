import { Request, Response } from 'express';
import { async<PERSON><PERSON><PERSON>, sendError, sendSuccess } from '../helpers/apiResponse';
import logger from '../helpers/logger';
import {
  validateAddress,
  validateAgainstSchema,
  validateContact,
  validateNumber,
  validateObject,
  validateShipmentDetails,
  validateString,
  ValidationSchema
} from '../helpers/validation';
import { sanitizeRequestBody } from '../middleware/requestLogger';
import { aramexService, IAramexRateRequest } from '../services/aramexService';
import { IShippingRequest, shippingService } from '../services/shippingService';

// ==========================================
// RECOMMENDED: Schema-Based Validation
// ==========================================

const createShipmentSchema: ValidationSchema = {
  shipper: {
    type: 'object',
    required: true,
    customValidator: (value, fieldName) => {
      validateContact(value, fieldName);
      validateAddress(value, fieldName);
    }
  },
  consignee: {
    type: 'object',
    required: true,
    customValidator: (value, fieldName) => {
      validateContact(value, fieldName);
      validateAddress(value, fieldName);
    }
  },
  shipmentDetails: {
    type: 'object',
    required: true,
    customValidator: (value, fieldName) => {
      validateShipmentDetails(value, fieldName);
    }
  },
  references: {
    type: 'object',
    required: false
  }
};

const rateCalculationSchema: ValidationSchema = {
  OriginAddress: {
    type: 'object',
    required: true,
    customValidator: (value, fieldName) => {
      validateObject(value, fieldName);
      validateString(value.Line1, `${fieldName}.Line1`, 1, 100);
      validateString(value.City, `${fieldName}.City`, 1, 50);
      validateString(value.CountryCode, `${fieldName}.CountryCode`, 2, 3);
    }
  },
  DestinationAddress: {
    type: 'object',
    required: true,
    customValidator: (value, fieldName) => {
      validateObject(value, fieldName);
      validateString(value.Line1, `${fieldName}.Line1`, 1, 100);
      validateString(value.City, `${fieldName}.City`, 1, 50);
      validateString(value.CountryCode, `${fieldName}.CountryCode`, 2, 3);
    }
  },
  ShipmentDetails: {
    type: 'object',
    required: true,
    customValidator: (value, fieldName) => {
      validateObject(value, fieldName);
      if (value.ActualWeight?.Value) {
        validateNumber(value.ActualWeight.Value, `${fieldName}.ActualWeight.Value`, 0.1);
      }
      if (value.ChargeableWeight?.Value) {
        validateNumber(value.ChargeableWeight.Value, `${fieldName}.ChargeableWeight.Value`, 0.1);
      }
    }
  },
  PreferredCurrencyCode: {
    type: 'string',
    required: true,
    minLength: 3,
    maxLength: 3,
    pattern: /^[A-Z]{3}$/
  }
};

export class ShippingController {
  /**
   * Create a new shipment
   */
  public createShipment = asyncHandler(async (req: Request, res: Response) => {
    logger.info('Create shipment request received', {
      method: req.method,
      path: req.path,
      query: req.query,
      params: req.params,
      body: sanitizeRequestBody(req.body), // Log sanitized request data
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      requestId: (req as any).requestId
    });

    // Validate request
    validateAgainstSchema(req.body, createShipmentSchema);

    // Log validation success
    logger.info('Request validation passed', {
      requestId: (req as any).requestId,
      validationFields: Object.keys(createShipmentSchema)
    });

    try {
      // Process shipment creation
      const result = await shippingService.createShipment(req.body);

      // Log successful response
      logger.info('Shipment creation completed', {
        requestId: (req as any).requestId,
        success: result.success,
        trackingNumber: result.trackingNumber,
        responseSize: JSON.stringify(result).length
      });

      sendSuccess(res, result.message, {
        shipment: result.data,
        trackingNumber: result.trackingNumber,
        reference: result.reference
      });
    } catch (error: any) {
      // Log error with full context
      logger.error('Shipment creation failed', {
        requestId: (req as any).requestId,
        error: error.message,
        stack: error.stack,
        requestBody: sanitizeRequestBody(req.body),
        ip: req.ip
      });
      throw error;
    }
  });

  /**
   * Calculate shipping rate
   */
  public calculateRate = asyncHandler(async (req: Request, res: Response) => {
    logger.info('Calculate rate request received', {
      method: req.method,
      url: req.url,
      ip: req.ip
    });

    // Validate request body
    validateObject(req.body, 'request body');
    validateObject(req.body.OriginAddress, 'OriginAddress');
    validateObject(req.body.DestinationAddress, 'DestinationAddress');
    validateObject(req.body.ShipmentDetails, 'ShipmentDetails');

    validateString(req.body.OriginAddress.Line1, 'OriginAddress.Line1');
    validateString(req.body.OriginAddress.City, 'OriginAddress.City');
    validateString(req.body.OriginAddress.CountryCode, 'OriginAddress.CountryCode');

    validateString(req.body.DestinationAddress.Line1, 'DestinationAddress.Line1');
    validateString(req.body.DestinationAddress.City, 'DestinationAddress.City');
    validateString(req.body.DestinationAddress.CountryCode, 'DestinationAddress.CountryCode');

    validateNumber(req.body.ShipmentDetails.ActualWeight.Value, 'ShipmentDetails.ActualWeight.Value', 0.1);
    validateNumber(req.body.ShipmentDetails.ChargeableWeight.Value, 'ShipmentDetails.ChargeableWeight.Value', 0.1);
    validateString(req.body.PreferredCurrencyCode, 'PreferredCurrencyCode');

    const rateRequest: IAramexRateRequest = {
      OriginAddress: req.body.OriginAddress,
      DestinationAddress: req.body.DestinationAddress,
      ShipmentDetails: req.body.ShipmentDetails,
      PreferredCurrencyCode: req.body.PreferredCurrencyCode,
    };
     // Calculate rate using service
    const result = await aramexService.calculateRate(rateRequest);

    if (result.success) {
      sendSuccess(res, result.message, result.data);
    } else {
      sendError(res, result.message, 400);
    }
  });

  /**
   * Calculate shipping cost estimate
   */
  public calculateCost = asyncHandler(async (req: Request, res: Response) => {
    logger.info('Calculate cost request received', {
      method: req.method,
      url: req.url,
      ip: req.ip
    });

    // Build shipping request for cost calculation
    const shippingRequest: IShippingRequest = req.body;

    // Calculate cost using service
    const result = await shippingService.calculateShippingCost(shippingRequest);

    if (result.success) {
      sendSuccess(res, result.message, {
        estimatedCost: result.cost,
        currency: result.currency
      });
    } else {
      sendError(res, result.message, 400);
    }
  });

  /**
   * Get shipment tracking information
   */
  public getTracking = asyncHandler(async (req: Request, res: Response) => {
    const { trackingNumber } = req.params;

    logger.info('Get tracking request received', {
      method: req.method,
      url: req.url,
      trackingNumber,
      ip: req.ip
    });

    if (!trackingNumber) {
      return sendError(res, 'Tracking number is required', 400);
    }

    // This would typically call Aramex tracking API
    // For now, return placeholder response
    sendSuccess(res, 'Tracking information retrieved', {
      trackingNumber,
      status: 'In Transit',
      location: 'Dubai, UAE',
      estimatedDelivery: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
      events: [
        {
          date: new Date().toISOString(),
          status: 'Picked up',
          location: 'Dubai, UAE'
        }
      ]
    });
  });
}

// Export singleton instance
export const shippingController = new ShippingController();
