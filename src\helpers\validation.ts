import { ApiError } from './apiResponse';

export const validateRequired = (value: any, fieldName: string): void => {
  if (value === undefined || value === null || value === '') {
    throw new ApiError(`${fieldName} is required`, 400);
  }
};

export const validateString = (value: any, fieldName: string, minLength: number = 1, maxLength: number = 1000): void => {
  validateRequired(value, fieldName);
  if (typeof value !== 'string') {
    throw new ApiError(`${fieldName} must be a string`, 400);
  }
  if (value.length < minLength) {
    throw new ApiError(`${fieldName} must be at least ${minLength} characters long`, 400);
  }
  if (value.length > maxLength) {
    throw new ApiError(`${fieldName} must not exceed ${maxLength} characters`, 400);
  }
};

export const validateEmail = (email: string, fieldName: string = 'email'): void => {
  validateRequired(email, fieldName);
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    throw new ApiError(`${fieldName} must be a valid email address`, 400);
  }
};

export const validatePhoneNumber = (phone: string, fieldName: string = 'phone'): void => {
  validateRequired(phone, fieldName);
  // Basic phone validation - can be enhanced based on requirements
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  if (!phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''))) {
    throw new ApiError(`${fieldName} must be a valid phone number`, 400);
  }
};

export const validateNumber = (value: any, fieldName: string, min?: number, max?: number): number => {
  validateRequired(value, fieldName);
  const num = Number(value);
  if (isNaN(num)) {
    throw new ApiError(`${fieldName} must be a valid number`, 400);
  }
  if (min !== undefined && num < min) {
    throw new ApiError(`${fieldName} must be at least ${min}`, 400);
  }
  if (max !== undefined && num > max) {
    throw new ApiError(`${fieldName} must not exceed ${max}`, 400);
  }
  return num;
};

export const validateObject = (value: any, fieldName: string): void => {
  validateRequired(value, fieldName);
  if (typeof value !== 'object' || Array.isArray(value)) {
    throw new ApiError(`${fieldName} must be an object`, 400);
  }
};

export const validateArray = (value: any, fieldName: string, minLength: number = 0): void => {
  validateRequired(value, fieldName);
  if (!Array.isArray(value)) {
    throw new ApiError(`${fieldName} must be an array`, 400);
  }
  if (value.length < minLength) {
    throw new ApiError(`${fieldName} must contain at least ${minLength} items`, 400);
  }
};

export const sanitizeString = (value: string): string => {
  if (typeof value !== 'string') return '';
  return value.trim().replace(/[<>]/g, '');
};

// Enhanced validation functions for complex objects

export const validateShipmentDetails = (details: any, fieldName: string = 'shipmentDetails'): void => {
  validateObject(details, fieldName);

  validateNumber(details.weight, `${fieldName}.weight`, 0.1, 1000);
  validateObject(details.dimensions, `${fieldName}.dimensions`);

  validateNumber(details.dimensions.length, `${fieldName}.dimensions.length`, 1, 300);
  validateNumber(details.dimensions.width, `${fieldName}.dimensions.width`, 1, 300);
  validateNumber(details.dimensions.height, `${fieldName}.dimensions.height`, 1, 300);

  validateNumber(details.numberOfPieces, `${fieldName}.numberOfPieces`, 1, 100);

  if (details.internalOrderId) {
    validateString(details.internalOrderId, `${fieldName}.internalOrderId`, 1, 50);
  }
  if (details.customerCharges) {
    validateNumber(details.customerCharges, `${fieldName}.customerCharges`, 0);
  }
};

export const validateAddress = (address: any, fieldName: string = 'address'): void => {
  validateObject(address, fieldName);

  validateString(address.addressLine1 || address.line1, `${fieldName}.addressLine1`, 1, 100);
  validateString(address.city, `${fieldName}.city`, 1, 50);
  validateString(address.countryCode, `${fieldName}.countryCode`, 2, 3);

  if (address.addressLine2) {
    validateString(address.addressLine2, `${fieldName}.addressLine2`, 1, 100);
  }
  if (address.stateOrProvinceCode) {
    validateString(address.stateOrProvinceCode, `${fieldName}.stateOrProvinceCode`, 1, 50);
  }
  if (address.postCode) {
    validateString(address.postCode, `${fieldName}.postCode`, 1, 20);
  }
};

export const validateContact = (contact: any, fieldName: string = 'contact'): void => {
  validateObject(contact, fieldName);

  validateString(contact.personName, `${fieldName}.personName`, 1, 100);
  validatePhoneNumber(contact.phoneNumber || contact.cellPhone, `${fieldName}.phoneNumber`);

  if (contact.companyName) {
    validateString(contact.companyName, `${fieldName}.companyName`, 1, 100);
  }
  if (contact.emailAddress) {
    validateEmail(contact.emailAddress, `${fieldName}.emailAddress`);
  }
};

// Advanced validation: Schema-based validation
export interface ValidationSchema {
  [key: string]: {
    type: 'string' | 'number' | 'boolean' | 'object' | 'array';
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    min?: number;
    max?: number;
    pattern?: RegExp;
    customValidator?: (value: any, fieldName: string) => void;
  };
}

export const validateAgainstSchema = (data: any, schema: ValidationSchema, parentField: string = ''): void => {
  for (const [field, rules] of Object.entries(schema)) {
    const fieldName = parentField ? `${parentField}.${field}` : field;
    const value = data[field];

    // Check required fields
    if (rules.required && (value === undefined || value === null || value === '')) {
      throw new ApiError(`${fieldName} is required`, 400);
    }

    // Skip validation if field is not required and not provided
    if (!rules.required && (value === undefined || value === null || value === '')) {
      continue;
    }

    // Type validation
    switch (rules.type) {
      case 'string':
        validateString(value, fieldName, rules.minLength, rules.maxLength);
        if (rules.pattern && !rules.pattern.test(value)) {
          throw new ApiError(`${fieldName} format is invalid`, 400);
        }
        break;
      case 'number':
        validateNumber(value, fieldName, rules.min, rules.max);
        break;
      case 'boolean':
        if (typeof value !== 'boolean') {
          throw new ApiError(`${fieldName} must be a boolean`, 400);
        }
        break;
      case 'object':
        validateObject(value, fieldName);
        break;
      case 'array':
        validateArray(value, fieldName, rules.minLength);
        break;
    }

    // Custom validation
    if (rules.customValidator) {
      rules.customValidator(value, fieldName);
    }
  }
};

// Validation middleware factory
export const createValidationMiddleware = (schema: ValidationSchema) => {
  return (req: any, res: any, next: any) => {
    try {
      validateAgainstSchema(req.body, schema);
      next();
    } catch (error) {
      next(error);
    }
  };
};
