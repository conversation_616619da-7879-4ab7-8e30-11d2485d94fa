export interface IAddress {
  Line1: string | null;
  Line2: string | null;
  Line3: string | null;
  City: string;
  StateOrProvinceCode: string | null;
  PostCode: string;
  CountryCode: string;
  Longitude: number | null;
  Latitude: number | null;
  BuildingNumber: string | null;
  BuildingName: string | null;
  Floor: string | null;
  Apartment: string | null;
  POBox: string | null;
  Description: string | null;
}

export interface IContact {
  Department: string | null;
  PersonName: string;
  Title: string | null;
  CompanyName: string | null;
  PhoneNumber1: string;
  PhoneNumber1Ext: string | null;
  PhoneNumber2: string | null;
  PhoneNumber2Ext: string | null;
  FaxNumber: string | null;
  CellPhone: string | null;
  EmailAddress: string;
  Type: string | null;
}

export interface IParty {
  accountNumber?: string | null;
  Reference1?: string | null;
  Reference2?: string | null;
  AccountNumber?: string;
  PartyAddress: IAddress;
  Contact: IContact;
}

export interface IWeight {
  Unit: string;
  Value: number;
}

export interface IShipmentDetails {
  productGroup: string; // "DOM" for domestic, "INT" for international
  productType: string; // "OND" for overnight delivery, "CDS" for documents
  descriptionOfGoods: string;
  numberOfPieces: number;
  weight: number; // in KG
  dimensions: {
    length: number; // in CM
    width: number;
    height: number;
  };

  ActualWeight?: IWeight;
  ChargeableWeight?: IWeight;
  customsValue?: number;
  insuranceAmount?: number;
  services?: string; // Additional services like "CODS,FIRST,FRDM"
  deliveryInstructions?: string;
  // Internal billing fields for your application
  internalOrderId?: string; // Your internal order/transaction ID
  customerCharges?: number; // What you charge your customer (not sent to Aramex)

  GoodsOriginCountry?: null;
}

export interface ICreateShippingRequest {
  shipper: {
    companyName: string;
    personName: string;
    phoneNumber: string;
    cellPhone?: string;
    emailAddress: string;
    addressLine1: string;
    addressLine2?: string;
    city: string;
    postCode: string;
    countryCode: string;
    stateOrProvinceCode?: string;
  };
  consignee: {
    companyName: string;
    personName: string;
    phoneNumber: string;
    cellPhone?: string;
    emailAddress: string;
    addressLine1: string;
    addressLine2?: string;
    city: string;
    postCode: string;
    countryCode: string;
    stateOrProvinceCode?: string;
  };
  shipmentDetails: IShipmentDetails;
  // shipmentDetails: {
  //   productGroup: string; // "DOM" for domestic, "INT" for international
  //   productType: string; // "OND" for overnight delivery, "CDS" for documents
  //   descriptionOfGoods: string;
  //   numberOfPieces: number;
  //   weight: number; // in KG
  //   dimensions: {
  //     length: number; // in CM
  //     width: number;
  //     height: number;
  //   };
  //   customsValue?: number;
  //   insuranceAmount?: number;
  //   services?: string; // Additional services like "CODS,FIRST,FRDM"
  //   deliveryInstructions?: string;
  //   // Internal billing fields for your application
  //   internalOrderId?: string; // Your internal order/transaction ID
  //   customerCharges?: number; // What you charge your customer (not sent to Aramex)
  // };
  references?: {
    reference1?: string;
    reference2?: string;
    reference3?: string;
  };
}

export interface IDeliveryRateRequest {
  OriginAddress: IAddress;
  DestinationAddress: IAddress;
  ShipmentDetails: IShipmentDetails;
  PreferredCurrencyCode: string;
}
