import * as express from "express";
import { shippingController } from "../controllers/shippingController";

class ShippingRoute {
  public router = express.Router();

  constructor() {
    this.initRoutes();
  }

  public initRoutes() {
    // Create shipment endpoint
    this.router.post("/create-shipping", shippingController.createShipment);

    // Calculate shipping rate endpoint
    this.router.post("/delivery-rate", shippingController.calculateRate);

    // Calculate shipping cost endpoint
    this.router.post("/calculate-cost", shippingController.calculateCost);

    // Get shipment tracking endpoint
    this.router.get("/tracking/:trackingNumber", shippingController.getTracking);
  }
}

export default ShippingRoute;
