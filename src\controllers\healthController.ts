import { Request, Response } from 'express';
import { sendSuccess, asyncHandler } from '../helpers/apiResponse';
import logger from '../helpers/logger';
import config from '../config/appConfig';

export class HealthController {
  /**
   * Health check endpoint
   */
  public healthCheck = asyncHandler(async (req: Request, res: Response) => {
    logger.info('Health check request', {
      method: req.method,
      url: req.url,
      ip: req.ip
    });

    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      environment: config.app.env,
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      services: {
        database: 'connected', // This could be enhanced with actual DB health check
        aramex: 'configured' // This could be enhanced with actual API health check
      }
    };

    sendSuccess(res, 'Service is healthy', healthData);
  });

  /**
   * Ping endpoint (lightweight health check)
   */
  public ping = asyncHandler(async (req: Request, res: Response) => {
    sendSuccess(res, 'pong', { timestamp: new Date().toISOString() });
  });

  /**
   * Service information endpoint
   */
  public serviceInfo = asyncHandler(async (req: Request, res: Response) => {
    logger.info('Service info request', {
      method: req.method,
      url: req.url,
      ip: req.ip
    });

    const info = {
      name: 'Delivery Microservice',
      version: '1.0.0',
      description: 'Aramex delivery service integration',
      environment: config.app.env,
      endpoints: {
        webhook: 'POST /webhook',
        health: 'GET /health',
        ping: 'GET /ping',
        createShipment: 'POST /create-shipping',
        createPickup: 'POST /create-pickup',
        calculateRate: 'POST /delivery-rate',
        webhookLogs: 'GET /webhooks/logs',
        webhookStats: 'GET /webhooks/stats'
      },
      features: [
        'Shipment creation',
        'Pickup creation',
        'Rate calculation',
        'Webhook processing',
        'Tracking integration'
      ]
    };

    sendSuccess(res, 'Service information retrieved', info);
  });
}

// Export singleton instance
export const healthController = new HealthController();
