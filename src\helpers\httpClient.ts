import axios, { AxiosResponse } from 'axios';
import { ApiError } from './apiResponse';
import logger from './logger';
import config from '../config/appConfig';

export interface HttpRequestOptions {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  url: string;
  headers?: Record<string, string>;
  data?: any;
  timeout?: number;
}

export interface HttpResponse<T = any> {
  status: number;
  statusText: string;
  headers: Record<string, string>;
  data: T;
}

export class HttpClient {
  private static instance: HttpClient;

  public static getInstance(): HttpClient {
    if (!HttpClient.instance) {
      HttpClient.instance = new HttpClient();
    }
    return HttpClient.instance;
  }

  public async request<T = any>(options: HttpRequestOptions): Promise<HttpResponse<T>> {
    try {
      const startTime = Date.now();

      const axiosConfig = {
        method: options.method,
        url: options.url,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        data: options.data,
        timeout: options.timeout || 30000, // 30 seconds default
      };

      logger.debug(`Making ${options.method} request to ${options.url}`);

      const response: AxiosResponse<T> = await axios(axiosConfig);

      const duration = Date.now() - startTime;
      logger.info(`Request completed in ${duration}ms`, {
        method: options.method,
        url: options.url,
        status: response.status,
      });

      return {
        status: response.status,
        statusText: response.statusText,
        headers: response.headers as Record<string, string>,
        data: response.data,
      };
    } catch (error: any) {
      const duration = Date.now() - Date.now(); // Would need to capture start time properly

      if (error.response) {
        // Server responded with error status
        logger.error(`Request failed with status ${error.response.status}`, {
          method: options.method,
          url: options.url,
          status: error.response.status,
          data: error.response.data,
        });

        throw new ApiError(
          error.response.data?.message || 'External API request failed',
          error.response.status
        );
      } else if (error.request) {
        // Network error
        logger.error('Network error occurred', {
          method: options.method,
          url: options.url,
          error: error.message,
        });
        throw new ApiError('Network error - unable to reach external service', 503);
      } else {
        // Other error
        logger.error('Request error', {
          method: options.method,
          url: options.url,
          error: error.message,
        });
        throw new ApiError('Request failed', 500);
      }
    }
  }
}

export const httpClient = HttpClient.getInstance();

// Utility functions for date/time operations
export const addDays = (date: Date, days: number): Date => {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
};

export const addHours = (date: Date, hours: number): Date => {
  const result = new Date(date);
  result.setTime(result.getTime() + hours * 60 * 60 * 1000);
  return result;
};

export const formatDateForApi = (date: Date): string => {
  return `/Date(${date.getTime()})/`;
};

// Utility for generating unique identifiers
export const generateId = (prefix: string = ''): string => {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substr(2, 9);
  return `${prefix}${timestamp}${random}`.toUpperCase();
};

// Utility for calculating volumetric weight
export const calculateVolumetricWeight = (length: number, width: number, height: number, divisor: number = 5000): number => {
  return (length * width * height) / divisor;
};

export const calculateChargeableWeight = (actualWeight: number, volumetricWeight: number): number => {
  return Math.max(actualWeight, volumetricWeight);
};
