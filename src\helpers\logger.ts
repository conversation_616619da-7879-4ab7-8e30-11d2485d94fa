import * as fs from 'fs';
import * as path from 'path';

interface LogEntry {
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  data?: any;
  timestamp: string;
}

class Logger {
  private logsDir: string;
  private logFile: string;
  private errorLogFile: string;

  constructor() {
    this.logsDir = path.join(process.cwd(), 'logs');
    this.logFile = path.join(this.logsDir, 'app.log');
    this.errorLogFile = path.join(this.logsDir, 'error.log');

    // Ensure logs directory exists
    if (!fs.existsSync(this.logsDir)) {
      fs.mkdirSync(this.logsDir, { recursive: true });
    }
  }

  private formatMessage(level: LogEntry['level'], message: string, data?: any): string {
    const timestamp = new Date().toISOString();
    const baseMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`;

    if (data) {
      return `${baseMessage} ${JSON.stringify(data, null, 2)}`;
    }

    return baseMessage;
  }

  private writeToFile(filePath: string, message: string): void {
    try {
      fs.appendFileSync(filePath, message + '\n');
    } catch (error) {
      console.error('Failed to write to log file:', error);
    }
  }

  public info(message: string, data?: any): void {
    const formattedMessage = this.formatMessage('info', message, data);
    console.log(formattedMessage);
    this.writeToFile(this.logFile, formattedMessage);
  }

  public warn(message: string, data?: any): void {
    const formattedMessage = this.formatMessage('warn', message, data);
    console.warn(formattedMessage);
    this.writeToFile(this.logFile, formattedMessage);
  }

  public error(message: string, error?: any): void {
    const errorData = error instanceof Error
      ? { message: error.message, stack: error.stack }
      : error;

    const formattedMessage = this.formatMessage('error', message, errorData);
    console.error(formattedMessage);

    // Write to both general log and error-specific log
    this.writeToFile(this.logFile, formattedMessage);
    this.writeToFile(this.errorLogFile, formattedMessage);
  }

  public debug(message: string, data?: any): void {
    if (process.env.NODE_ENV === 'development') {
      const formattedMessage = this.formatMessage('debug', message, data);
      console.debug(formattedMessage);
      this.writeToFile(this.logFile, formattedMessage);
    }
  }

  public request(method: string, url: string, statusCode?: number): void {
    const message = `${method} ${url}${statusCode ? ` - ${statusCode}` : ''}`;
    this.info(message);
  }

  // Utility method to check if file logging is working
  public testFileLogging(): void {
    const testMessage = this.formatMessage('info', 'File logging test - verifying log files are writable');
    console.log('Testing file logging...');

    try {
      this.writeToFile(this.logFile, testMessage);
      this.writeToFile(this.errorLogFile, this.formatMessage('info', 'Error log test'));

      console.log('✅ File logging test successful!');
      console.log(`📁 Check logs in: ${this.logsDir}`);
      console.log(`📄 General logs: ${this.logFile}`);
      console.log(`📄 Error logs: ${this.errorLogFile}`);
    } catch (error) {
      console.error('❌ File logging test failed:', error);
    }
  }

  // Method to get log file paths
  public getLogPaths(): { dir: string; general: string; error: string } {
    return {
      dir: this.logsDir,
      general: this.logFile,
      error: this.errorLogFile
    };
  }
}

export const logger = new Logger();
export default logger;
