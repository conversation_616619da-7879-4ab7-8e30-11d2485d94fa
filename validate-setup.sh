#!/bin/bash

# Batayeq Delivery Microservice - Setup Validation Script
# This script validates that the microservice is properly configured and running

echo "🚀 Batayeq Delivery Microservice - Setup Validation"
echo "=================================================="

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

# Check if Node.js is installed
echo "📋 Checking prerequisites..."
node --version > /dev/null 2>&1
print_status $? "Node.js installed"

# Check if npm is installed
npm --version > /dev/null 2>&1
print_status $? "npm installed"

# Check if MongoDB is running (if local)
echo "📋 Checking services..."
if command -v mongod &> /dev/null; then
    pgrep mongod > /dev/null 2>&1
    print_status $? "MongoDB service running"
else
    echo -e "${YELLOW}⚠️  MongoDB not found locally (will use docker if available)${NC}"
fi

# Check if dependencies are installed
echo "📋 Checking project setup..."
if [ -d "node_modules" ]; then
    print_status 0 "Dependencies installed"
else
    echo -e "${RED}❌ Dependencies not installed${NC}"
    echo "   Run: npm install"
    exit 1
fi

# Check if environment file exists
if [ -f ".env" ]; then
    print_status 0 "Environment file exists"
else
    echo -e "${YELLOW}⚠️  Environment file not found${NC}"
    echo "   Copy .env.example to .env and configure your settings"
fi

# Check if TypeScript compiles
echo "📋 Checking TypeScript compilation..."
npm run build > /dev/null 2>&1
print_status $? "TypeScript compilation"

# Check API endpoints
echo "📋 Testing API endpoints..."
if [ -f "dist/server.js" ]; then
    # Start the server in background for testing
    npm start > /dev/null 2>&1 &
    SERVER_PID=$!

    # Wait for server to start
    sleep 5

    # Test health endpoint
    curl -s http://localhost:3000/ping > /dev/null 2>&1
    print_status $? "Health endpoint (/ping) accessible"

    # Stop the server
    kill $SERVER_PID 2>/dev/null
    wait $SERVER_PID 2>/dev/null
else
    echo -e "${RED}❌ Built application not found${NC}"
    echo "   Run: npm run build"
fi

echo ""
echo "🎉 Setup validation complete!"
echo ""
echo "📚 Next steps:"
echo "   1. Configure your .env file with actual values"
echo "   2. Start MongoDB service: mongod"
echo "   3. Run the application: npm run dev"
echo "   4. Test the API: curl http://localhost:3000/health"
echo ""
echo "📖 Documentation:"
echo "   - API Documentation: API_DOCUMENTATION.md"
echo "   - Setup Guide: README.md"
echo "   - Environment Config: .env.example"
