{"PickupAddress": {"Line1": "Test Address", "Line2": "Test Address Line 2", "Line3": "", "City": "Dubai", "StateOrProvinceCode": "Dubai", "PostCode": "", "CountryCode": "AE"}, "PickupContact": {"EmailAddress": "<EMAIL>", "CellPhone": "97148707700", "PersonName": "Test Person Name", "CompanyName": "Test Company Name", "PhoneNumber1": "97148707700", "Department": "Test Department"}, "PickupLocation": "Reception", "PickupDate": "2025-09-25T10:00:00.000Z", "ReadyTime": "2025-09-25T09:00:00.000Z", "LastPickupTime": "2025-09-25T17:00:00.000Z", "ClosingTime": "2025-09-25T18:00:00.000Z", "PickupItems": [{"ProductGroup": "DOM", "ProductType": "ONP", "NumberOfShipments": 1, "PackageType": "Box", "Payment": "P", "ShipmentWeight": {"Unit": "KG", "Value": 0.5}, "ShipmentVolume": null, "NumberOfPieces": 1, "CashAmount": null, "ExtraCharges": null, "ShipmentDimensions": {"Length": 0, "Width": 0, "Height": 0, "Unit": ""}, "Comments": "Airway Bill Number:44097846262"}], "Status": "Ready", "ExistingShipments": null, "Comments": "Test pickup request", "Vehicle": "Bike", "References": {"Reference1": "001", "Reference2": "Test Reference"}}