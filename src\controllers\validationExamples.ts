import { Request, Response } from 'express';
import { asyncHandler, sendSuccess, sendError, ApiError } from '../helpers/apiResponse';
import {
  validateObject,
  validateString,
  validateNumber,
  validateEmail,
  validateArray,
  validateShipmentDetails,
  validateAddress,
  validateContact,
  validateAgainstSchema,
  createValidationMiddleware,
  ValidationSchema
} from '../helpers/validation';
import logger from '../helpers/logger';

// ==========================================
// APPROACH 1: Manual Field-by-Field Validation (Current Approach)
// ==========================================

export const manualValidationExample = asyncHandler(async (req: Request, res: Response) => {
  // Basic request validation
  validateObject(req.body, 'request body');

  // Individual field validation
  validateString(req.body.name, 'name', 2, 100);
  validateEmail(req.body.email, 'email');
  validateNumber(req.body.age, 'age', 18, 120);

  if (req.body.address) {
    validateObject(req.body.address, 'address');
    validateString(req.body.address.street, 'address.street');
    validateString(req.body.address.city, 'address.city');
  }

  // Process request
  sendSuccess(res, 'Manual validation passed', { validated: true });
});

// ==========================================
// APPROACH 2: Using Enhanced Validation Functions
// ==========================================

export const enhancedValidationExample = asyncHandler(async (req: Request, res: Response) => {
  // Validate entire request body
  validateObject(req.body, 'request body');

  // Use specialized validation functions
  if (req.body.shipper) {
    validateContact(req.body.shipper, 'shipper');
    validateAddress(req.body.shipper, 'shipper');
  }

  if (req.body.consignee) {
    validateContact(req.body.consignee, 'consignee');
    validateAddress(req.body.consignee, 'consignee');
  }

  if (req.body.shipmentDetails) {
    validateShipmentDetails(req.body.shipmentDetails, 'shipmentDetails');
  }

  // Process request
  sendSuccess(res, 'Enhanced validation passed', { validated: true });
});

// ==========================================
// APPROACH 3: Schema-Based Validation (Recommended)
// ==========================================

// Define validation schema
const createShipmentSchema: ValidationSchema = {
  shipper: {
    type: 'object',
    required: true,
    customValidator: (value, fieldName) => {
      validateContact(value, fieldName);
      validateAddress(value, fieldName);
    }
  },
  consignee: {
    type: 'object',
    required: true,
    customValidator: (value, fieldName) => {
      validateContact(value, fieldName);
      validateAddress(value, fieldName);
    }
  },
  shipmentDetails: {
    type: 'object',
    required: true,
    customValidator: (value, fieldName) => {
      validateShipmentDetails(value, fieldName);
    }
  },
  references: {
    type: 'object',
    required: false
  }
};

export const schemaValidationExample = asyncHandler(async (req: Request, res: Response) => {
  // Validate against schema
  validateAgainstSchema(req.body, createShipmentSchema);

  // Additional business logic validation
  const { shipper, consignee } = req.body;

  // Ensure shipper and consignee are not the same
  if (shipper.emailAddress && consignee.emailAddress &&
      shipper.emailAddress === consignee.emailAddress) {
    throw new Error('Shipper and consignee cannot have the same email address');
  }

  // Process request
  sendSuccess(res, 'Schema validation passed', { validated: true });
});

// ==========================================
// APPROACH 4: Validation Middleware (Best Practice)
// ==========================================

// Create validation middleware
export const shipmentValidationMiddleware = createValidationMiddleware(createShipmentSchema);

// Controller that uses validation middleware
export const middlewareValidationExample = [
  shipmentValidationMiddleware,
  asyncHandler(async (req: Request, res: Response) => {
    // Request is already validated by middleware
    logger.info('Processing validated shipment request', {
      shipper: req.body.shipper.personName,
      consignee: req.body.consignee.personName
    });

    // Process request
    sendSuccess(res, 'Middleware validation passed', {
      shipper: req.body.shipper,
      consignee: req.body.consignee,
      shipmentDetails: req.body.shipmentDetails
    });
  })
];

// ==========================================
// APPROACH 5: Advanced Schema with Complex Rules
// ==========================================

const advancedShipmentSchema: ValidationSchema = {
  shipper: {
    type: 'object',
    required: true,
    customValidator: (value, fieldName) => {
      validateContact(value, fieldName);
      validateAddress(value, fieldName);

      // Business rule: Company name required for shipments over $1000
      if (value.customerCharges > 1000 && !value.companyName) {
        throw new Error(`${fieldName}.companyName is required for shipments over $1000`);
      }
    }
  },
  consignee: {
    type: 'object',
    required: true,
    customValidator: (value, fieldName) => {
      validateContact(value, fieldName);
      validateAddress(value, fieldName);
    }
  },
  shipmentDetails: {
    type: 'object',
    required: true,
    customValidator: (value, fieldName) => {
      validateShipmentDetails(value, fieldName);

      // Business rule: Weight cannot exceed dimensions-based calculation
      const { weight, dimensions } = value;
      const volumeWeight = (dimensions.length * dimensions.width * dimensions.height) / 5000;
      if (weight > volumeWeight * 1.2) {
        throw new Error(`${fieldName}.weight seems unrealistic for given dimensions`);
      }
    }
  },
  references: {
    type: 'object',
    required: false,
    customValidator: (value, fieldName) => {
      if (value && Object.keys(value).length > 5) {
        throw new Error(`${fieldName} cannot have more than 5 reference fields`);
      }
    }
  },
  priority: {
    type: 'string',
    required: false,
    pattern: /^(standard|express|urgent)$/,
    customValidator: (value, fieldName) => {
      // Note: Business rules requiring req object should be validated in controller
      if (value === 'urgent') {
        // This would need to be checked in the controller after validation
        console.log(`${fieldName}: Additional urgent shipping validation needed in controller`);
      }
    }
  }
};

// ==========================================
// APPROACH 6: Conditional Validation
// ==========================================

export const conditionalValidationExample = asyncHandler(async (req: Request, res: Response) => {
  validateObject(req.body, 'request body');

  const { shipmentType, paymentMethod } = req.body;

  // Conditional validation based on shipment type
  if (shipmentType === 'international') {
    validateString(req.body.customsDeclaration, 'customsDeclaration', 10, 1000);
    validateString(req.body.exportLicense, 'exportLicense', 5, 50);
  } else if (shipmentType === 'domestic') {
    // Domestic shipments might have different requirements
    validateString(req.body.internalReference, 'internalReference', 1, 100);
  }

  // Conditional validation based on payment method
  if (paymentMethod === 'credit_card') {
    validateString(req.body.cardNumber, 'cardNumber', 13, 19);
    validateString(req.body.expiryDate, 'expiryDate', 5, 5);
    validateString(req.body.cvv, 'cvv', 3, 4);
  } else if (paymentMethod === 'bank_transfer') {
    validateString(req.body.accountNumber, 'accountNumber', 8, 20);
    validateString(req.body.routingNumber, 'routingNumber', 9, 9);
  }

  sendSuccess(res, 'Conditional validation passed', { validated: true });
});

// ==========================================
// APPROACH 7: Array Validation
// ==========================================

export const arrayValidationExample = asyncHandler(async (req: Request, res: Response) => {
  validateObject(req.body, 'request body');
  validateArray(req.body.items, 'items', 1); // At least 1 item

  // Validate each item in the array
  req.body.items.forEach((item: any, index: number) => {
    const itemFieldName = `items[${index}]`;

    validateObject(item, itemFieldName);
    validateString(item.name, `${itemFieldName}.name`, 1, 100);
    validateNumber(item.quantity, `${itemFieldName}.quantity`, 1, 1000);
    validateNumber(item.weight, `${itemFieldName}.weight`, 0.1, 100);

    if (item.description) {
      validateString(item.description, `${itemFieldName}.description`, 1, 500);
    }
  });

  sendSuccess(res, 'Array validation passed', {
    itemCount: req.body.items.length,
    totalWeight: req.body.items.reduce((sum: number, item: any) => sum + item.weight, 0)
  });
});

// ==========================================
// APPROACH 8: Custom Validation Classes
// ==========================================

export class ShipmentValidator {
  private errors: string[] = [];

  validateCreateShipment(data: any): boolean {
    this.errors = [];

    try {
      // Use your existing validation functions
      validateObject(data, 'request');

      if (data.shipper) {
        validateContact(data.shipper, 'shipper');
        validateAddress(data.shipper, 'shipper');
      }

      if (data.consignee) {
        validateContact(data.consignee, 'consignee');
        validateAddress(data.consignee, 'consignee');
      }

      if (data.shipmentDetails) {
        validateShipmentDetails(data.shipmentDetails, 'shipmentDetails');
      }

      // Custom business rules
      this.validateBusinessRules(data);

      return this.errors.length === 0;
    } catch (error: any) {
      this.errors.push(error.message);
      return false;
    }
  }

  private validateBusinessRules(data: any): void {
    // Business rule 1: International shipments need passport info
    if (data.shipper?.countryCode !== data.consignee?.countryCode) {
      if (!data.shipper?.passportNumber) {
        this.errors.push('Passport number required for international shipments');
      }
    }

    // Business rule 2: High-value shipments need insurance
    if (data.shipmentDetails?.customerCharges > 5000) {
      if (!data.insurance?.opted) {
        this.errors.push('Insurance required for shipments over $5000');
      }
    }

    // Business rule 3: Fragile items need special packaging
    if (data.shipmentDetails?.fragile && !data.specialPackaging) {
      this.errors.push('Special packaging required for fragile items');
    }
  }

  getErrors(): string[] {
    return this.errors;
  }
}

export const classBasedValidationExample = asyncHandler(async (req: Request, res: Response) => {
  const validator = new ShipmentValidator();

  if (!validator.validateCreateShipment(req.body)) {
    const error = new ApiError(`Validation failed: ${validator.getErrors().join(', ')}`, 400);
    return sendError(res, error);
  }

  sendSuccess(res, 'Class-based validation passed', { validated: true });
});
