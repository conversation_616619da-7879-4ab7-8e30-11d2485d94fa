import * as dotenv from 'dotenv';
import { ApiError } from '../helpers/apiResponse';

dotenv.config();

interface AppConfig {
  port: number;
  env: string;
}

interface DatabaseConfig {
  url: string;
}

interface AramexConfig {
  baseUrl: string;
  adminUserName: string;
  adminPassword: string;
  adminAccountNumber: string;
  adminAccountPin: string;
  adminAccountEntity: string;
  adminAccountCountryCode: string;
  clientInfo: {
    UserName: string;
    Password: string;
    Version: string;
    AccountNumber: string;
    AccountPin: string;
    AccountEntity: string;
    AccountCountryCode: string;
    Source: number;
    PreferredLanguageCode: string | null;
  };
}

interface ServicesConfig {
  authentication: {
    baseUrl: string;
  };
  application: {
    baseUrl: string;
  };
}

interface Config {
  domain: string;
  app: AppConfig;
  dataBase: DatabaseConfig;
  aramex: AramexConfig;
  services: ServicesConfig;
}

const validateEnvVar = (key: string, defaultValue?: string): string => {
  const value = process.env[key];
  if (!value && !defaultValue) {
    throw new ApiError(`Environment variable ${key} is required`, 500);
  }
  return value || defaultValue!;
};

const validateNumberEnvVar = (key: string, defaultValue?: number): number => {
  const value = validateEnvVar(key, defaultValue?.toString());
  const num = parseInt(value, 10);
  if (isNaN(num)) {
    throw new ApiError(`Environment variable ${key} must be a valid number`, 500);
  }
  return num;
};

const config: Config = {
  domain: validateEnvVar('DOMAIN', ''),
  app: {
    port: validateNumberEnvVar('APP_PORT'),
    env: validateEnvVar('NODE_ENV', 'development'),
  },
  dataBase: {
    url: validateEnvVar('MONGODB_URL'),
  },
  aramex: {
    baseUrl: validateEnvVar('ARAMEX_BASE_URL'),
    adminUserName: validateEnvVar('ARAMEX_ADMIN_USER_NAME'),
    adminPassword: validateEnvVar('ARAMEX_ADMIN_PASSWORD'),
    adminAccountNumber: validateEnvVar('ARAMEX_ADMIN_ACCOUNT_NUMBER'),
    adminAccountPin: validateEnvVar('ARAMEX_ADMIN_ACCOUNT_PIN'),
    adminAccountEntity: validateEnvVar('ARAMEX_ADMIN_ACCOUNT_ENTITY'),
    adminAccountCountryCode: validateEnvVar('ARAMEX_ADMIN_ACCOUNT_COUNTRY_CODE'),
    clientInfo: {
      UserName: validateEnvVar('ARAMEX_ADMIN_USER_NAME'),
      Password: validateEnvVar('ARAMEX_ADMIN_PASSWORD'),
      Version: 'v1.0',
      AccountNumber: validateEnvVar('ARAMEX_ADMIN_ACCOUNT_NUMBER'),
      AccountPin: validateEnvVar('ARAMEX_ADMIN_ACCOUNT_PIN'),
      AccountEntity: validateEnvVar('ARAMEX_ADMIN_ACCOUNT_ENTITY'),
      AccountCountryCode: validateEnvVar('ARAMEX_ADMIN_ACCOUNT_COUNTRY_CODE'),
      Source: 0,
      PreferredLanguageCode: null,
    },
  },
  services: {
    authentication: {
      baseUrl: validateEnvVar('AUTHENTICATION_URL', ''),
    },
    application: {
      baseUrl: validateEnvVar('APPLICATION_URL', ''),
    },
  },
};

export default config;
