import * as express from "express";
import { webhookController } from "../controllers/webhookController";

class AramexWebhookRoute {
  public router = express.Router();

  constructor() {
    this.initRoutes();
  }

  public initRoutes() {
    // Main webhook endpoint
    this.router.post("/webhook", webhookController.handleWebhook);

    // Webhook logs endpoint
    this.router.get("/logs", webhookController.getWebhookLogs);

    // Webhook statistics endpoint
    this.router.get("/stats", webhookController.getWebhookStats);

    // Test webhook endpoint
    this.router.post("/test", webhookController.testWebhook);
  }
}

export default AramexWebhookRoute;
