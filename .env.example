# Environment Configuration Template
# Copy this file to .env and fill in your actual values

# ======================================
# APPLICATION SETTINGS
# ======================================
APP_PORT=3000
NODE_ENV=development
DOMAIN=localhost

# ======================================
# DATABASE CONFIGURATION
# ======================================
MONGODB_URL=mongodb://localhost:27017/batayeq_delivery

# ======================================
# JWT AUTHENTICATION
# ======================================
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
JWT_ACCESS_EXPIRY=15m
JWT_REFRESH_EXPIRY=7d

# ======================================
# ARAMEX INTEGRATION
# ======================================
ARAMEX_BASE_URL=https://ws.aramex.net/ShippingAPI.V2/
ARAMEX_ADMIN_USER_NAME=your_aramex_username
ARAMEX_ADMIN_PASSWORD=your_aramex_password
ARAMEX_ADMIN_ACCOUNT_NUMBER=your_account_number
ARAMEX_ADMIN_ACCOUNT_PIN=your_account_pin
ARAMEX_ADMIN_ACCOUNT_ENTITY=your_account_entity
ARAMEX_ADMIN_ACCOUNT_COUNTRY_CODE=AE

# ======================================
# EXTERNAL SERVICES
# ======================================
AUTHENTICATION_URL=http://localhost:3001
APPLICATION_URL=http://localhost:3002

# ======================================
# LOGGING CONFIGURATION
# ======================================
LOG_LEVEL=info
LOG_FILE_PATH=logs/app.log

# ======================================
# SECURITY SETTINGS
# ======================================
CORS_ORIGINS=http://localhost:3000,http://localhost:8080
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# ======================================
# CACHE SETTINGS
# ======================================
REDIS_URL=redis://localhost:6379
CACHE_TTL=3600

# ======================================
# EMAIL CONFIGURATION (Optional)
# ======================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# ======================================
# MONITORING & METRICS (Optional)
# ======================================
ENABLE_METRICS=true
METRICS_PORT=9090
NEW_RELIC_LICENSE_KEY=your-new-relic-key
