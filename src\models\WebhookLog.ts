import mongoose, { Schema, Document } from 'mongoose';

export interface IWebhookLog extends Document {
  payload: any;
  receivedAt: Date;
  processed: boolean;
  processingError?: string;
  source?: string;
  createdAt: Date;
  updatedAt: Date;
}

const WebhookLogSchema = new Schema<IWebhookLog>({
  payload: {
    type: Schema.Types.Mixed,
    required: true
  },
  receivedAt: {
    type: Date,
    default: Date.now,
    required: true
  },
  processed: {
    type: Boolean,
    default: false
  },
  processingError: {
    type: String,
    trim: true
  },
  source: {
    type: String,
    trim: true,
    default: 'aramex'
  }
}, {
  timestamps: true,
  collection: 'webhook_logs'
});

// Indexes for better query performance
WebhookLogSchema.index({ receivedAt: -1 });
WebhookLogSchema.index({ processed: 1, receivedAt: -1 });
WebhookLogSchema.index({ source: 1 });

export const WebhookLog = mongoose.model<IWebhookLog>('WebhookLog', WebhookLogSchema);
