import * as jwt from 'jsonwebtoken';
import { User, IUser } from '../models/User';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '../models/ApiKey';
import logger from '../helpers/logger';
import config from '../config/appConfig';

export interface JWTPayload {
  userId: string;
  username: string;
  email: string;
  role: string;
  type: 'user' | 'api_key';
  iat?: number;
  exp?: number;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export class AuthService {
  private static instance: AuthService;
  private jwtSecret: string;
  private jwtRefreshSecret: string;
  private accessTokenExpiry: string;
  private refreshTokenExpiry: string;

  public static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  constructor() {
    this.jwtSecret = process.env.JWT_SECRET || 'your-super-secret-jwt-key';
    this.jwtRefreshSecret = process.env.JWT_REFRESH_SECRET || 'your-super-secret-refresh-key';
    this.accessTokenExpiry = process.env.JWT_ACCESS_EXPIRY || '15m';
    this.refreshTokenExpiry = process.env.JWT_REFRESH_EXPIRY || '7d';
  }

  /**
   * Authenticate user with username/email and password
   */
  public async authenticateUser(identifier: string, password: string): Promise<IUser> {
    try {
      // Simple user lookup for now - you can enhance this
      const user = await User.findOne({
        $or: [
          { username: identifier.toLowerCase() },
          { email: identifier.toLowerCase() }
        ],
        isActive: true
      });

      if (!user) {
        throw new Error('Invalid credentials');
      }

      const isPasswordValid = await user.comparePassword(password);
      if (!isPasswordValid) {
        throw new Error('Invalid credentials');
      }

      // Update last login
      user.lastLogin = new Date();
      await user.save();

      logger.info('User authenticated successfully', {
        userId: user._id,
        username: user.username
      });

      return user;
    } catch (error: any) {
      logger.error('User authentication failed', { identifier, error: error.message });
      throw error;
    }
  }

  /**
   * Authenticate API key
   */
  public async authenticateApiKey(apiKey: string): Promise<IApiKey> {
    try {
      const crypto = require('crypto');
      const hashedKey = crypto.createHash('sha256').update(apiKey).digest('hex');

      const key = await ApiKey.findOne({
        hashedKey,
        isActive: true
      });

      if (!key) {
        throw new Error('Invalid API key');
      }

      if (key.isExpired()) {
        throw new Error('API key has expired');
      }

      // Increment usage count
      await key.incrementUsage();

      logger.info('API key authenticated successfully', {
        keyId: key._id,
        name: key.name,
        permissions: key.permissions
      });

      return key;
    } catch (error: any) {
      logger.error('API key authentication failed', { error: error.message });
      throw error;
    }
  }

  /**
   * Generate JWT tokens for user
   */
  public generateUserTokens(user: IUser): AuthTokens {
    const payload: JWTPayload = {
      userId: user._id.toString(),
      username: user.username,
      email: user.email,
      role: user.role,
      type: 'user'
    };

    // Use any to bypass JWT typing issues
    const accessToken = (jwt as any).sign(payload, this.jwtSecret, {
      expiresIn: this.accessTokenExpiry
    });

    const refreshToken = (jwt as any).sign(
      { userId: user._id.toString(), type: 'refresh' },
      this.jwtRefreshSecret,
      { expiresIn: this.refreshTokenExpiry }
    );

    const expiresIn = this.parseExpiryToSeconds(this.accessTokenExpiry);

    return {
      accessToken,
      refreshToken,
      expiresIn
    };
  }

  /**
   * Generate JWT token for API key
   */
  public generateApiKeyToken(apiKey: IApiKey): string {
    const payload: JWTPayload = {
      userId: apiKey._id.toString(),
      username: apiKey.name,
      email: '', // API keys don't have email
      role: 'api_user',
      type: 'api_key'
    };

    return (jwt as any).sign(payload, this.jwtSecret, {
      expiresIn: this.accessTokenExpiry
    });
  }

  /**
   * Verify JWT token
   */
  public verifyToken(token: string): JWTPayload {
    try {
      const decoded = jwt.verify(token, this.jwtSecret) as JWTPayload;
      return decoded;
    } catch (error: any) {
      logger.error('Token verification failed', { error: error.message });
      throw new Error('Invalid or expired token');
    }
  }

  /**
   * Refresh access token using refresh token
   */
  public async refreshAccessToken(refreshToken: string): Promise<AuthTokens> {
    try {
      const decoded = jwt.verify(refreshToken, this.jwtRefreshSecret) as any;

      if (decoded.type !== 'refresh') {
        throw new Error('Invalid refresh token');
      }

      const user = await User.findById(decoded.userId);
      if (!user || !user.isActive) {
        throw new Error('User not found or inactive');
      }

      return this.generateUserTokens(user);
    } catch (error: any) {
      logger.error('Token refresh failed', { error: error.message });
      throw new Error('Invalid refresh token');
    }
  }

  /**
   * Hash API key for storage/comparison
   */
  private hashApiKey(apiKey: string): string {
    const crypto = require('crypto');
    return crypto.createHash('sha256').update(apiKey).digest('hex');
  }

  /**
   * Parse JWT expiry string to seconds
   */
  private parseExpiryToSeconds(expiry: string): number {
    const match = expiry.match(/^(\d+)([smhd])$/);
    if (!match) return 900; // Default 15 minutes

    const value = parseInt(match[1]);
    const unit = match[2];

    switch (unit) {
      case 's': return value;
      case 'm': return value * 60;
      case 'h': return value * 60 * 60;
      case 'd': return value * 60 * 60 * 24;
      default: return 900;
    }
  }

  /**
   * Check if user has required permission
   */
  public hasPermission(userRole: string, requiredPermission: string): boolean {
    const roleHierarchy = {
      'viewer': ['read'],
      'user': ['read', 'write'],
      'manager': ['read', 'write', 'manage'],
      'admin': ['read', 'write', 'manage', 'admin']
    };

    const userPermissions = roleHierarchy[userRole as keyof typeof roleHierarchy] || [];

    // Check for exact permission match or wildcard permissions
    return userPermissions.includes(requiredPermission) ||
           userPermissions.includes('admin') ||
           userPermissions.includes('manage');
  }

  /**
   * Check if API key has required permission
   */
  public apiKeyHasPermission(apiKey: IApiKey, requiredPermission: string): boolean {
    return apiKey.permissions.includes(requiredPermission) ||
           apiKey.permissions.includes('admin:*') ||
           apiKey.permissions.includes('*');
  }
}

export const authService = AuthService.getInstance();
