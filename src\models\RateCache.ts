import mongoose, { Schema, Document } from 'mongoose';

export interface IRateCache extends Document {
  // Cache key components
  originCountry: string;
  originCity: string;
  destinationCountry: string;
  destinationCity: string;
  weight: number;
  serviceType: string;

  // Cached data
  rate: number;
  currency: string;
  breakdown?: {
    baseRate: number;
    fuelSurcharge?: number;
    securityFee?: number;
    otherFees?: number;
  };

  // Cache metadata
  cachedAt: Date;
  expiresAt: Date;
  source: string; // 'aramex', 'manual', etc.
  isValid: boolean;

  // Request context
  originalRequest?: any;
  aramexResponse?: any;
}

const RateCacheSchema = new Schema<IRateCache>({
  // Cache key
  originCountry: {
    type: String,
    required: true,
    uppercase: true,
    index: true
  },
  originCity: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  destinationCountry: {
    type: String,
    required: true,
    uppercase: true,
    index: true
  },
  destinationCity: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  weight: {
    type: Number,
    required: true,
    min: 0.1,
    index: true
  },
  serviceType: {
    type: String,
    required: true,
    default: 'DOM',
    index: true
  },

  // Cached rate data
  rate: {
    type: Number,
    required: true,
    min: 0
  },
  currency: {
    type: String,
    required: true,
    default: 'AED',
    uppercase: true
  },
  breakdown: {
    baseRate: { type: Number, min: 0 },
    fuelSurcharge: { type: Number, min: 0 },
    securityFee: { type: Number, min: 0 },
    otherFees: { type: Number, min: 0 }
  },

  // Cache management
  cachedAt: {
    type: Date,
    default: Date.now,
    required: true
  },
  expiresAt: {
    type: Date,
    required: true,
    index: true
  },
  source: {
    type: String,
    required: true,
    default: 'aramex'
  },
  isValid: {
    type: Boolean,
    default: true,
    index: true
  },

  // Store original request/response for debugging
  originalRequest: { type: Schema.Types.Mixed },
  aramexResponse: { type: Schema.Types.Mixed }
}, {
  timestamps: true,
  collection: 'rate_cache'
});

// Compound index for efficient lookups
RateCacheSchema.index({
  originCountry: 1,
  originCity: 1,
  destinationCountry: 1,
  destinationCity: 1,
  weight: 1,
  serviceType: 1,
  isValid: 1
});

// TTL index to automatically expire old cache entries
RateCacheSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

// Instance method to check if cache is expired
RateCacheSchema.methods.isExpired = function(): boolean {
  return new Date() > this.expiresAt;
};

// Instance method to extend cache expiry
RateCacheSchema.methods.extendExpiry = function(hours: number = 24): Promise<IRateCache> {
  this.expiresAt = new Date(Date.now() + hours * 60 * 60 * 1000);
  return this.save();
};

// Static method to find valid cached rate
RateCacheSchema.statics.findValidCache = function(
  originCountry: string,
  originCity: string,
  destinationCountry: string,
  destinationCity: string,
  weight: number,
  serviceType: string = 'DOM'
) {
  return this.findOne({
    originCountry: originCountry.toUpperCase(),
    originCity: originCity.trim(),
    destinationCountry: destinationCountry.toUpperCase(),
    destinationCity: destinationCity.trim(),
    weight: { $gte: weight * 0.95, $lte: weight * 1.05 }, // Allow 5% weight variation
    serviceType,
    isValid: true,
    expiresAt: { $gt: new Date() }
  }).sort({ cachedAt: -1 });
};

// Static method to invalidate cache for a route
RateCacheSchema.statics.invalidateRoute = function(
  originCountry: string,
  originCity: string,
  destinationCountry: string,
  destinationCity: string
) {
  return this.updateMany(
    {
      originCountry: originCountry.toUpperCase(),
      originCity: originCity.trim(),
      destinationCountry: destinationCountry.toUpperCase(),
      destinationCity: destinationCity.trim()
    },
    { isValid: false }
  );
};

// Static method to cleanup expired entries
RateCacheSchema.statics.cleanupExpired = function() {
  return this.deleteMany({
    expiresAt: { $lt: new Date() }
  });
};

export const RateCache = mongoose.model<IRateCache>('RateCache', RateCacheSchema);
