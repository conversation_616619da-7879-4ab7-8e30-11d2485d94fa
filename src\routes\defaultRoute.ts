import * as express from "express";
import { healthController } from "../controllers/healthController";

class DefaultRoute {
  public router = express.Router();

  constructor() {
    this.initRoutes();
  }

  public initRoutes() {
    // Health check endpoint
    this.router.get("/ping", healthController.ping);

    // Detailed health check endpoint
    this.router.get("/health", healthController.healthCheck);

    // Service information endpoint
    this.router.get("/info", healthController.serviceInfo);

    // Catch-all route for undefined endpoints
    this.router.use("*", (req: express.Request, res: express.Response) => {
      res.status(404).json({
        success: false,
        message: "Endpoint not found",
        error: `Route ${req.method} ${req.path} does not exist`,
        timestamp: new Date().toISOString(),
        statusCode: 404
      });
    });
  }
}

export default DefaultRoute;
